{"openapi": "3.0.0", "info": {"title": "Jobs Portal Employer API", "version": "1.0.0", "description": "Employer specific endpoints for the Jobs Portal"}, "servers": [{"url": "/api/v1"}], "paths": {"/employer/jobs": {"post": {"tags": ["Job Posting"], "summary": "Create a new job posting", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"title": {"type": "string"}, "department": {"type": "string"}, "description": {"type": "string"}, "requirements": {"type": "array", "items": {"type": "string"}}, "qualifications": {"type": "array", "items": {"type": "string"}}, "skills": {"type": "array", "items": {"type": "string"}}, "location": {"type": "string"}, "workType": {"type": "string", "enum": ["onsite", "remote", "hybrid"]}, "employmentType": {"type": "string", "enum": ["full-time", "part-time", "contract", "temporary"]}, "salary": {"type": "object", "properties": {"min": {"type": "number"}, "max": {"type": "number"}, "currency": {"type": "string"}, "period": {"type": "string", "enum": ["hourly", "monthly", "yearly"]}}}, "benefits": {"type": "array", "items": {"type": "string"}}, "screeningQuestions": {"type": "array", "items": {"type": "object", "properties": {"question": {"type": "string"}, "type": {"type": "string", "enum": ["text", "multipleChoice", "yesNo"]}, "options": {"type": "array", "items": {"type": "string"}}, "required": {"type": "boolean"}}}}, "applicationDeadline": {"type": "string", "format": "date"}, "status": {"type": "string", "enum": ["draft", "published", "closed"]}}, "required": ["title", "description", "location", "employmentType"]}}}}, "responses": {"201": {"description": "Job posting created successfully"}, "400": {"description": "Invalid input"}}}, "get": {"tags": ["Job Posting"], "summary": "Get all job postings for the employer", "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "status", "schema": {"type": "string", "enum": ["draft", "published", "paused", "closed"]}}, {"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 20}}], "responses": {"200": {"description": "List of job postings retrieved successfully"}}}}, "/employer/jobs/{jobId}": {"put": {"tags": ["Job Posting"], "summary": "Update an existing job posting", "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "jobId", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobPosting"}}}}, "responses": {"200": {"description": "Job posting updated successfully"}, "404": {"description": "Job posting not found"}}}, "delete": {"tags": ["Job Posting"], "summary": "Delete a job posting", "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "jobId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Job posting deleted successfully"}, "404": {"description": "Job posting not found"}}}}, "/employer/jobs/{jobId}/duplicate": {"post": {"tags": ["Job Posting"], "summary": "Duplicate an existing job posting", "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "jobId", "required": true, "schema": {"type": "string"}}], "responses": {"201": {"description": "Job posting duplicated successfully"}, "404": {"description": "Original job posting not found"}}}}, "/employer/jobs/{jobId}/status": {"put": {"tags": ["Job Posting"], "summary": "Update job posting status", "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "jobId", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "enum": ["published", "paused", "closed"]}, "reason": {"type": "string"}}, "required": ["status"]}}}}, "responses": {"200": {"description": "Job posting status updated successfully"}}}}, "/employer/jobs/{jobId}/analytics": {"get": {"tags": ["Job Posting"], "summary": "Get analytics for a job posting", "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "jobId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Analytics retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"views": {"type": "integer"}, "applications": {"type": "integer"}, "conversionRate": {"type": "number"}, "averageQualificationScore": {"type": "number"}, "applicationsByStage": {"type": "object"}, "applicationTrends": {"type": "array", "items": {"type": "object"}}}}}}}}}}, "/employer/applications": {"get": {"tags": ["Application Management"], "summary": "Get job applications", "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "jobId", "schema": {"type": "string"}}, {"in": "query", "name": "status", "schema": {"type": "string", "enum": ["new", "reviewed", "shortlisted", "interviewing", "offered", "hired", "rejected"]}}, {"in": "query", "name": "sortBy", "schema": {"type": "string", "enum": ["date", "rating", "qualification"]}}, {"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 20}}], "responses": {"200": {"description": "Applications retrieved successfully"}}}}, "/employer/applications/{applicationId}/status": {"put": {"tags": ["Application Management"], "summary": "Update application status", "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "applicationId", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "enum": ["reviewed", "shortlisted", "interviewing", "offered", "hired", "rejected"]}, "comments": {"type": "string"}, "rating": {"type": "integer", "minimum": 1, "maximum": 5}}, "required": ["status"]}}}}, "responses": {"200": {"description": "Application status updated successfully"}}}}, "/employer/applications/{applicationId}/communication": {"post": {"tags": ["Candidate Communication"], "summary": "Send message to candidate", "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "applicationId", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"messageType": {"type": "string", "enum": ["message", "statusUpdate", "interviewInvite"]}, "subject": {"type": "string"}, "content": {"type": "string"}, "interviewDetails": {"type": "object", "properties": {"date": {"type": "string", "format": "date-time"}, "location": {"type": "string"}, "type": {"type": "string", "enum": ["in<PERSON><PERSON>", "phone", "video"]}, "notes": {"type": "string"}}}}, "required": ["messageType", "content"]}}}}, "responses": {"201": {"description": "Message sent successfully"}}}, "get": {"tags": ["Candidate Communication"], "summary": "Get communication history", "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "applicationId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Communication history retrieved successfully"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}