# Collection: notifications

## Description

Stores user notifications with support for multiple notification types and delivery channels.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the notification                         |   X   |    X     |
| userId         | ObjectId      | User receiving the notification                                |   X   |    X     |
| type           | String        | Notification type (job_match, application_update, etc.)        |   X   |    X     |
| title          | String        | Notification title                                             |         |    X     |
| message        | String        | Notification content                                           |         |    X     |
| priority       | String        | Priority level (high, medium, low)                             |   X   |    X     |
| status         | String        | Status (unread, read, archived)                                |   X   |    X     |
| actionUrl      | String        | URL for notification action                                    |         |          |
| metadata       | Object        | Additional context data                                        |         |          |
| channels       | [String]      | Delivery channels (in_app, email, push)                        |         |    X     |
| deliveryStatus | Object        | Delivery status for each channel                               |         |          |
| expiresAt      | Date          | Expiration timestamp                                           |   X   |          |
| createdAt      | Date          | Creation timestamp                                             |   X   |    X     |
| readAt         | Date          | When notification was read                                     |   X   |          |

## Delivery Status Sub-document Schema

```json
{
  "in_app": {
    "status": String,
    "deliveredAt": Date
  },
  "email": {
    "status": String,
    "deliveredAt": Date,
    "emailId": String
  },
  "push": {
    "status": String,
    "deliveredAt": Date,
    "deviceIds": [String]
  }
}
```

## Indexing Strategy

* **Compound index:** `userId`, `status`, `createdAt` (for user's notification feed)
* **Compound index:** `userId`, `type` (for filtering by notification type)
* **TTL index:** `expiresAt` (for automatic notification cleanup)
* **Compound index:** `status`, `priority` (for notification processing)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "userId": ObjectId("..."),
  "type": "application_update",
  "title": "Application Status Updated",
  "message": "Your application for Senior Developer position has been shortlisted",
  "priority": "high",
  "status": "unread",
  "actionUrl": "/applications/123",
  "metadata": {
    "applicationId": ObjectId("..."),
    "jobTitle": "Senior Developer",
    "companyName": "Tech Corp"
  },
  "channels": ["in_app", "email"],
  "deliveryStatus": {
    "in_app": {
      "status": "delivered",
      "deliveredAt": ISODate("2024-03-20T10:00:00Z")
    },
    "email": {
      "status": "sent",
      "deliveredAt": ISODate("2024-03-20T10:00:01Z"),
      "emailId": "notify_123"
    }
  },
  "expiresAt": ISODate("2024-04-20"),
  "createdAt": ISODate("2024-03-20"),
  "readAt": null
}
```