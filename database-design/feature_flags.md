# Collection: feature_flags

## Description

Manages feature flags for controlled feature rollouts, A/B testing, and feature toggles.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the feature flag                         |   X   |    X     |
| name           | String        | Feature flag name                                              |   X   |    X     |
| key            | String        | Unique identifier for the feature (slug)                       |   X   |    X     |
| description    | String        | Feature description                                            |         |    X     |
| type           | String        | Flag type (boolean, multivariate, percentage)                  |   X   |    X     |
| status         | String        | Flag status (active, inactive, archived)                       |   X   |    X     |
| value          | Mixed         | Current value/state of the feature                             |         |    X     |
| rules          | [Object]      | Targeting rules and conditions                                 |         |          |
| variants       | [Object]      | Variants for A/B testing                                       |         |          |
| rolloutConfig  | Object        | Gradual rollout configuration                                  |         |          |
| dependencies   | [String]      | Dependencies on other feature flags                            |         |          |
| environments   | Object        | Environment-specific configurations                             |         |          |
| metrics        | [Object]      | Associated metrics for tracking                                |         |          |
| metadata       | Object        | Additional flag metadata                                       |         |          |
| createdBy      | ObjectId      | Reference to user who created the flag                        |   X   |    X     |
| updatedBy      | ObjectId      | Reference to user who last updated the flag                   |   X   |    X     |
| createdAt      | Date          | Flag creation timestamp                                        |   X   |    X     |
| updatedAt      | Date          | Last update timestamp                                          |   X   |    X     |

## Rules Sub-document Schema

```json
{
  "conditions": [{
    "attribute": String,
    "operator": String,
    "value": Mixed
  }],
  "value": Mixed,
  "priority": Number,
  "description": String
}
```

## Variants Sub-document Schema

```json
{
  "key": String,
  "name": String,
  "description": String,
  "value": Mixed,
  "weight": Number,
  "metadata": Object
}
```

## Rollout Config Sub-document Schema

```json
{
  "startDate": Date,
  "endDate": Date,
  "percentage": Number,
  "strategy": String,
  "stickiness": String,
  "segments": [String]
}
```

## Environments Sub-document Schema

```json
{
  "development": {
    "status": String,
    "value": Mixed,
    "updatedAt": Date
  },
  "staging": {
    "status": String,
    "value": Mixed,
    "updatedAt": Date
  },
  "production": {
    "status": String,
    "value": Mixed,
    "updatedAt": Date
  }
}
```

## Indexing Strategy

* **Unique index:** `key` (for flag lookup)
* **Compound index:** `status`, `type` (for active flags)
* **Compound index:** `dependencies` (for dependency tracking)
* **Index:** `updatedAt` (for change tracking)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "name": "New Search Experience",
  "key": "new-search-ui",
  "description": "Enhanced job search interface with filters",
  "type": "percentage",
  "status": "active",
  "value": true,
  "rules": [
    {
      "conditions": [
        {
          "attribute": "userType",
          "operator": "equals",
          "value": "premium"
        },
        {
          "attribute": "country",
          "operator": "in",
          "value": ["US", "CA"]
        }
      ],
      "value": true,
      "priority": 1,
      "description": "Enable for premium users in NA"
    }
  ],
  "variants": [
    {
      "key": "control",
      "name": "Current Search UI",
      "description": "Existing search interface",
      "value": false,
      "weight": 50,
      "metadata": {
        "uiVersion": "1.0"
      }
    },
    {
      "key": "treatment",
      "name": "New Search UI",
      "description": "Enhanced search interface",
      "value": true,
      "weight": 50,
      "metadata": {
        "uiVersion": "2.0"
      }
    }
  ],
  "rolloutConfig": {
    "startDate": ISODate("2024-03-01"),
    "endDate": ISODate("2024-04-01"),
    "percentage": 25,
    "strategy": "gradual",
    "stickiness": "userId",
    "segments": ["beta_users", "early_adopters"]
  },
  "dependencies": [
    "enhanced-filters",
    "new-backend-api"
  ],
  "environments": {
    "development": {
      "status": "active",
      "value": true,
      "updatedAt": ISODate("2024-03-15")
    },
    "staging": {
      "status": "active",
      "value": true,
      "updatedAt": ISODate("2024-03-18")
    },
    "production": {
      "status": "active",
      "value": false,
      "updatedAt": ISODate("2024-03-20")
    }
  },
  "metrics": [
    {
      "name": "search_conversion_rate",
      "type": "percentage",
      "goal": "increase",
      "threshold": 5
    },
    {
      "name": "average_search_time",
      "type": "duration",
      "goal": "decrease",
      "threshold": 500
    }
  ],
  "metadata": {
    "owner": "Search Team",
    "jiraTicket": "SEARCH-123",
    "rollbackPlan": "Disable flag and revert to old search API",
    "notes": "Part of Q1 search improvement initiative"
  },
  "createdBy": ObjectId("..."),
  "updatedBy": ObjectId("..."),
  "createdAt": ISODate("2024-03-01"),
  "updatedAt": ISODate("2024-03-20")
}