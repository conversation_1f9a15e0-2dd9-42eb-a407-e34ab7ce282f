# Collection: background_jobs

## Description

Manages asynchronous background tasks and their execution states.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the job                                  |   X   |    X     |
| name           | String        | Job name/type                                                  |   X   |    X     |
| queue          | String        | Queue name                                                     |   X   |    X     |
| priority       | Number        | Job priority (higher number = higher priority)                 |   X   |    X     |
| status         | String        | Job status (pending, running, completed, failed)               |   X   |    X     |
| data           | Object        | Job input data                                                 |         |    X     |
| result         | Object        | Job output data                                                |         |          |
| progress       | Object        | Job progress information                                       |         |          |
| attempts       | Number        | Number of execution attempts                                   |   X   |    X     |
| maxAttempts    | Number        | Maximum number of retry attempts                               |         |    X     |
| error          | Object        | Error information if failed                                    |         |          |
| options        | Object        | Job execution options                                          |         |          |
| schedule       | Object        | Scheduling information for recurring jobs                      |         |          |
| locks          | [String]      | Resource locks held by the job                                |   X   |          |
| dependencies   | [ObjectId]    | Dependencies on other jobs                                     |   X   |          |
| metadata       | Object        | Additional job metadata                                        |         |          |
| createdBy      | ObjectId      | Reference to user who created the job                         |   X   |    X     |
| startedAt      | Date          | Job start timestamp                                           |   X   |          |
| completedAt    | Date          | Job completion timestamp                                       |   X   |          |
| createdAt      | Date          | Job creation timestamp                                         |   X   |    X     |
| updatedAt      | Date          | Last update timestamp                                          |   X   |    X     |

## Progress Sub-document Schema

```json
{
  "current": Number,
  "total": Number,
  "percentage": Number,
  "message": String,
  "step": String
}
```

## Error Sub-document Schema

```json
{
  "code": String,
  "message": String,
  "stack": String,
  "attempt": Number,
  "timestamp": Date
}
```

## Options Sub-document Schema

```json
{
  "timeout": Number,
  "retryDelay": Number,
  "backoff": {
    "type": String,
    "delay": Number
  },
  "removeOnComplete": Boolean,
  "removeOnFail": Boolean
}
```

## Schedule Sub-document Schema

```json
{
  "type": String,
  "cron": String,
  "timezone": String,
  "startDate": Date,
  "endDate": Date,
  "repeat": {
    "every": Number,
    "limit": Number
  }
}
```

## Indexing Strategy

* **Compound index:** `status`, `queue` (for job processing)
* **Compound index:** `name`, `createdAt` (for job history)
* **Compound index:** `status`, `priority` (for job scheduling)
* **TTL index:** `completedAt` (for job cleanup)

## Sample Documents

```json
{
  "_id": ObjectId("..."),
  "name": "process_resume",
  "queue": "resume_processing",
  "priority": 2,
  "status": "running",
  "data": {
    "resumeId": ObjectId("..."),
    "userId": ObjectId("..."),
    "options": {
      "extractSkills": true,
      "generateSummary": true
    }
  },
  "result": null,
  "progress": {
    "current": 3,
    "total": 5,
    "percentage": 60,
    "message": "Extracting skills from resume",
    "step": "skill_extraction"
  },
  "attempts": 1,
  "maxAttempts": 3,
  "error": null,
  "options": {
    "timeout": 300000,
    "retryDelay": 60000,
    "backoff": {
      "type": "exponential",
      "delay": 1000
    },
    "removeOnComplete": false,
    "removeOnFail": false
  },
  "schedule": null,
  "locks": [
    "resume:123",
    "user:456"
  ],
  "dependencies": [
    ObjectId("...")
  ],
  "metadata": {
    "source": "web_upload",
    "fileType": "pdf",
    "fileSize": 1024576,
    "priority_reason": "premium_user"
  },
  "createdBy": ObjectId("..."),
  "startedAt": ISODate("2024-03-20T15:30:00Z"),
  "completedAt": null,
  "createdAt": ISODate("2024-03-20T15:29:55Z"),
  "updatedAt": ISODate("2024-03-20T15:30:00Z")
}
```

```json
{
  "_id": ObjectId("..."),
  "name": "sync_job_listings",
  "queue": "integration_sync",
  "priority": 1,
  "status": "pending",
  "data": {
    "provider": "linkedin",
    "companyId": "12345",
    "options": {
      "fullSync": true,
      "daysBack": 30
    }
  },
  "result": null,
  "progress": null,
  "attempts": 0,
  "maxAttempts": 5,
  "error": null,
  "options": {
    "timeout": 600000,
    "retryDelay": 300000,
    "backoff": {
      "type": "fixed",
      "delay": 300000
    },
    "removeOnComplete": true,
    "removeOnFail": false
  },
  "schedule": {
    "type": "recurring",
    "cron": "0 0 * * *",
    "timezone": "UTC",
    "startDate": ISODate("2024-03-20"),
    "endDate": null,
    "repeat": {
      "every": 86400,
      "limit": null
    }
  },
  "locks": [
    "integration:linkedin",
    "company:12345"
  ],
  "dependencies": [],
  "metadata": {
    "integration_type": "job_sync",
    "expected_items": 1000,
    "last_sync": ISODate("2024-03-19T15:30:00Z")
  },
  "createdBy": ObjectId("..."),
  "startedAt": null,
  "completedAt": null,
  "createdAt": ISODate("2024-03-20T15:30:00Z"),
  "updatedAt": ISODate("2024-03-20T15:30:00Z")
}
```

``` 