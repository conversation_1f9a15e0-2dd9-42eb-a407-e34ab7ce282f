# Collection: user_preferences

## Description

Stores user-specific preferences and settings.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the preferences                          |   X   |    X     |
| userId         | ObjectId      | Reference to user                                              |   X   |    X     |
| notifications  | Object        | Notification preferences                                        |         |    X     |
| privacy        | Object        | Privacy settings                                               |         |    X     |
| communication  | Object        | Communication preferences                                       |         |    X     |
| display        | Object        | Display preferences                                            |         |          |
| jobAlerts      | Object        | Job alert preferences                                          |         |          |
| accessibility  | Object        | Accessibility settings                                         |         |          |
| language       | String        | Preferred language                                             |   X   |    X     |
| timezone       | String        | User's timezone                                                |         |    X     |
| theme          | String        | UI theme preference                                            |         |          |
| createdAt      | Date          | Preferences creation timestamp                                 |   X   |    X     |
| updatedAt      | Date          | Last update timestamp                                          |   X   |    X     |

## Notification Preferences Schema

```json
{
  "email": {
    "jobAlerts": Boolean,
    "applicationUpdates": Boolean,
    "messages": Boolean,
    "marketing": Boolean,
    "frequency": String
  },
  "push": {
    "enabled": Boolean,
    "jobAlerts": Boolean,
    "applicationUpdates": Boolean,
    "messages": Boolean
  },
  "inApp": {
    "enabled": Boolean,
    "soundEnabled": Boolean,
    "desktop": Boolean
  }
}
```

## Privacy Settings Schema

```json
{
  "profileVisibility": String,
  "searchable": Boolean,
  "showOnlineStatus": Boolean,
  "allowMessaging": String,
  "dataSharing": {
    "analytics": Boolean,
    "thirdParty": Boolean,
    "recruiters": Boolean
  }
}
```

## Indexing Strategy

* **Unique index:** `userId` (one preference document per user)
* **Compound index:** `userId`, `updatedAt` (for sync purposes)
* **Compound index:** `language`, `timezone` (for localization)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "userId": ObjectId("..."),
  "notifications": {
    "email": {
      "jobAlerts": true,
      "applicationUpdates": true,
      "messages": true,
      "marketing": false,
      "frequency": "daily"
    },
    "push": {
      "enabled": true,
      "jobAlerts": true,
      "applicationUpdates": true,
      "messages": true
    },
    "inApp": {
      "enabled": true,
      "soundEnabled": false,
      "desktop": true
    }
  },
  "privacy": {
    "profileVisibility": "public",
    "searchable": true,
    "showOnlineStatus": true,
    "allowMessaging": "connections_only",
    "dataSharing": {
      "analytics": true,
      "thirdParty": false,
      "recruiters": true
    }
  },
  "communication": {
    "preferredMethod": "email",
    "marketingOptIn": false,
    "newsletterSubscription": true
  },
  "display": {
    "densityLevel": "comfortable",
    "fontSize": "medium",
    "colorScheme": "system"
  },
  "jobAlerts": {
    "frequency": "daily",
    "maxAlerts": 10,
    "deliveryTime": "09:00"
  },
  "accessibility": {
    "highContrast": false,
    "reducedMotion": false,
    "screenReader": false
  },
  "language": "en-US",
  "timezone": "America/Los_Angeles",
  "theme": "light",
  "createdAt": ISODate("2024-03-01"),
  "updatedAt": ISODate("2024-03-20")
}
``` 