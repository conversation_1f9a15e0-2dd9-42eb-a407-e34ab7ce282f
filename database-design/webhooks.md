# Collection: webhooks

## Description

Manages webhook configurations and delivery logs for external system integrations.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the webhook                              |   X   |    X     |
| userId         | ObjectId      | Reference to user who owns the webhook                         |   X   |    X     |
| name           | String        | Webhook name/description                                       |   X   |    X     |
| url            | String        | Endpoint URL                                                   |   X   |    X     |
| events         | [String]      | Event types to trigger webhook                                 |   X   |    X     |
| status         | String        | Webhook status (active, paused, disabled)                      |   X   |    X     |
| secret         | String        | Webhook signing secret (hashed)                                |         |    X     |
| version        | String        | API version for payload format                                 |   X   |    X     |
| retryConfig    | Object        | Retry configuration                                            |         |          |
| filters        | Object        | Event filtering criteria                                       |         |          |
| headers        | Object        | Custom HTTP headers                                            |         |          |
| lastDelivery   | Date          | Last successful delivery timestamp                             |   X   |          |
| errorCount     | Number        | Count of consecutive failures                                  |   X   |          |
| metadata       | Object        | Additional webhook metadata                                     |         |          |
| createdAt      | Date          | Webhook creation timestamp                                     |   X   |    X     |
| updatedAt      | Date          | Last update timestamp                                          |   X   |    X     |

## Retry Config Sub-document Schema

```json
{
  "maxAttempts": Number,
  "initialDelay": Number,
  "maxDelay": Number,
  "backoffMultiplier": Number,
  "retryOnTimeout": Boolean
}
```

## Filters Sub-document Schema

```json
{
  "conditions": [{
    "field": String,
    "operator": String,
    "value": Mixed
  }],
  "matchType": String
}
```

## Metadata Sub-document Schema

```json
{
  "description": String,
  "team": String,
  "environment": String,
  "contactEmail": String,
  "stats": {
    "totalDeliveries": Number,
    "successRate": Number,
    "averageLatency": Number
  },
  "lastError": {
    "timestamp": Date,
    "message": String,
    "code": String
  }
}
```

## Indexing Strategy

* **Compound index:** `userId`, `status` (for active webhooks)
* **Compound index:** `events`, `status` (for event processing)
* **Index:** `lastDelivery` (for monitoring)
* **Index:** `errorCount` (for health checks)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "userId": ObjectId("..."),
  "name": "New Application Notifications",
  "url": "https://api.example.com/webhooks/jobs",
  "events": [
    "application.created",
    "application.updated",
    "application.status_changed"
  ],
  "status": "active",
  "secret": "$2b$10$X7ZKj9k2R8B...",
  "version": "2024-03",
  "retryConfig": {
    "maxAttempts": 5,
    "initialDelay": 30,
    "maxDelay": 3600,
    "backoffMultiplier": 2,
    "retryOnTimeout": true
  },
  "filters": {
    "conditions": [
      {
        "field": "job.department",
        "operator": "in",
        "value": ["Engineering", "Product"]
      },
      {
        "field": "application.status",
        "operator": "equals",
        "value": "submitted"
      }
    ],
    "matchType": "all"
  },
  "headers": {
    "X-Custom-Token": "abc123",
    "User-Agent": "JobsPortal-Webhook/1.0"
  },
  "lastDelivery": ISODate("2024-03-20T15:30:00Z"),
  "errorCount": 0,
  "metadata": {
    "description": "Sends notifications for new job applications",
    "team": "Recruiting",
    "environment": "production",
    "contactEmail": "<EMAIL>",
    "stats": {
      "totalDeliveries": 5000,
      "successRate": 99.8,
      "averageLatency": 245
    },
    "lastError": null
  },
  "createdAt": ISODate("2024-03-01"),
  "updatedAt": ISODate("2024-03-20")
}
``` 