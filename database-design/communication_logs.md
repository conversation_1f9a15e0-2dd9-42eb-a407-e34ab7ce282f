# Collection: communication_logs

## Description

Tracks all communications between employers and job seekers.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the communication                        |   X   |    X     |
| applicationId  | ObjectId      | Reference to the job application                               |   X   |    X     |
| senderId       | ObjectId      | ID of the sender                                              |   X   |    X     |
| recipientId    | ObjectId      | ID of the recipient                                           |   X   |    X     |
| messageType    | String        | Type of communication (email, in-app, interview_invite)        |   X   |    X     |
| subject        | String        | Message subject                                                |         |          |
| content        | String        | Message content                                                |         |    X     |
| status         | String        | Message status (sent, delivered, read)                         |   X   |    X     |
| attachments    | [Object]      | Array of attached files                                        |         |          |
| interviewDetails| Object       | Interview information if applicable                            |         |          |
| scheduledTime  | Date          | Scheduled interview time                                       |   X   |          |
| location       | String        | Interview location or link                                     |         |          |
| interviewType  | String        | Type of interview (in-person, video, phone)                    |         |          |
| metadata       | Object        | Additional contextual data                                     |         |          |
| createdAt      | Date          | Message creation timestamp                                     |   X   |    X     |
| updatedAt      | Date          | Last message update timestamp                                  |   X   |    X     |

## Attachment Sub-document Schema

```json
{
  "fileId": String,
  "fileName": String,
  "fileType": String,
  "fileSize": Number,
  "fileUrl": String,
  "uploadedAt": Date
}
```

## Interview Details Sub-document Schema

```json
{
  "duration": Number,
  "participants": [ObjectId],
  "platform": String,
  "meetingId": String,
  "joinUrl": String,
  "notes": String,
  "timezone": String
}
```

## Indexing Strategy

* **Compound index:** `applicationId`, `createdAt` (for application communication history)
* **Compound index:** `senderId`, `recipientId`, `createdAt` (for conversation threads)
* **Compound index:** `messageType`, `scheduledTime` (for upcoming interviews)
* **Text index:** `subject`, `content` (for message search)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "applicationId": ObjectId("..."),
  "senderId": ObjectId("..."),
  "recipientId": ObjectId("..."),
  "messageType": "interview_invite",
  "subject": "Interview Invitation - Senior Developer Position",
  "content": "We would like to invite you for a technical interview...",
  "status": "delivered",
  "attachments": [
    {
      "fileId": "file123",
      "fileName": "Interview_Details.pdf",
      "fileType": "application/pdf",
      "fileSize": 245760,
      "fileUrl": "https://storage.example.com/files/interview_123.pdf",
      "uploadedAt": ISODate("2024-03-20T10:00:00Z")
    }
  ],
  "interviewDetails": {
    "duration": 60,
    "participants": [ObjectId("..."), ObjectId("...")],
    "platform": "Zoom",
    "meetingId": "*********",
    "joinUrl": "https://zoom.us/j/*********",
    "notes": "Please prepare a coding demonstration",
    "timezone": "UTC-8"
  },
  "scheduledTime": ISODate("2024-03-25T15:00:00Z"),
  "location": "https://zoom.us/j/*********",
  "interviewType": "video",
  "metadata": {
    "jobTitle": "Senior Developer",
    "companyName": "Tech Corp",
    "stage": "Technical Interview"
  },
  "createdAt": ISODate("2024-03-20T10:00:00Z"),
  "updatedAt": ISODate("2024-03-20T10:00:00Z")
}
``` 