# Collection: cache_entries

## Description

Manages system-wide caching for improved performance and reduced database load.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the cache entry                          |   X   |    X     |
| key            | String        | Cache key                                                      |   X   |    X     |
| value          | Mixed         | Cached data                                                    |         |    X     |
| type           | String        | Cache type (query, computation, session, etc)                  |   X   |    X     |
| tags           | [String]      | Cache tags for invalidation                                    |   X   |          |
| ttl            | Number        | Time to live in seconds                                        |   X   |    X     |
| expiresAt      | Date          | Expiration timestamp                                           |   X   |    X     |
| lastAccessed   | Date          | Last access timestamp                                          |   X   |          |
| hitCount       | Number        | Number of cache hits                                           |   X   |          |
| size           | Number        | Size of cached data in bytes                                   |   X   |          |
| compression    | Object        | Compression details                                            |         |          |
| metadata       | Object        | Additional cache metadata                                      |         |          |
| createdAt      | Date          | Cache creation timestamp                                       |   X   |    X     |
| updatedAt      | Date          | Last update timestamp                                          |   X   |    X     |

## Compression Sub-document Schema

```json
{
  "algorithm": String,
  "ratio": Number,
  "originalSize": Number,
  "compressedSize": Number
}
```

## Metadata Sub-document Schema

```json
{
  "source": String,
  "version": String,
  "dependencies": [String],
  "invalidationRules": [String],
  "region": String,
  "environment": String
}
```

## Indexing Strategy

* **Unique index:** `key` (for cache lookup)
* **Compound index:** `type`, `tags` (for cache invalidation)
* **TTL index:** `expiresAt` (for automatic cache cleanup)
* **Index:** `lastAccessed` (for LRU cleanup)

## Sample Documents

```json
{
  "_id": ObjectId("..."),
  "key": "job_search:frontend:us:active",
  "value": {
    "results": [
      {
        "id": "job123",
        "title": "Senior Frontend Developer",
        "company": "Tech Corp"
      }
    ],
    "total": 150,
    "facets": {
      "locations": ["New York", "San Francisco"],
      "skills": ["React", "TypeScript"]
    }
  },
  "type": "query_result",
  "tags": [
    "job_search",
    "frontend",
    "us_region"
  ],
  "ttl": 3600,
  "expiresAt": ISODate("2024-03-20T16:30:00Z"),
  "lastAccessed": ISODate("2024-03-20T15:45:00Z"),
  "hitCount": 25,
  "size": 15360,
  "compression": {
    "algorithm": "gzip",
    "ratio": 0.4,
    "originalSize": 25600,
    "compressedSize": 15360
  },
  "metadata": {
    "source": "job_search_service",
    "version": "1.5.0",
    "dependencies": [
      "job_postings",
      "companies"
    ],
    "invalidationRules": [
      "on_job_update",
      "on_company_update"
    ],
    "region": "us",
    "environment": "production"
  },
  "createdAt": ISODate("2024-03-20T15:30:00Z"),
  "updatedAt": ISODate("2024-03-20T15:45:00Z")
}
```

```json
{
  "_id": ObjectId("..."),
  "key": "user_preferences:123",
  "value": {
    "theme": "dark",
    "notifications": {
      "email": true,
      "push": false
    },
    "jobAlerts": {
      "frequency": "daily",
      "keywords": ["frontend", "react"]
    }
  },
  "type": "session_data",
  "tags": [
    "user_123",
    "preferences"
  ],
  "ttl": 86400,
  "expiresAt": ISODate("2024-03-21T15:30:00Z"),
  "lastAccessed": ISODate("2024-03-20T15:30:00Z"),
  "hitCount": 5,
  "size": 512,
  "compression": null,
  "metadata": {
    "source": "user_service",
    "version": "1.0.0",
    "dependencies": [
      "user_preferences"
    ],
    "invalidationRules": [
      "on_preference_update"
    ],
    "region": "global",
    "environment": "production"
  },
  "createdAt": ISODate("2024-03-20T15:30:00Z"),
  "updatedAt": ISODate("2024-03-20T15:30:00Z")
}
``` 