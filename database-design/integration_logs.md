# Collection: integration_logs

## Description

Tracks all integration-related activities, events, and errors for external system integrations.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the log entry                            |   X   |    X     |
| integrationType | String       | Type of integration (webhook, api, oauth, etc)                 |   X   |    X     |
| integrationId  | ObjectId      | Reference to the integration configuration                     |   X   |    X     |
| eventType      | String        | Type of event being logged                                     |   X   |    X     |
| status         | String        | Status of the integration event                                |   X   |    X     |
| direction      | String        | Direction of the integration (inbound/outbound)                |   X   |    X     |
| requestData    | Object        | Request data details                                           |         |          |
| responseData   | Object        | Response data details                                          |         |          |
| error          | Object        | Error details if any                                           |         |          |
| duration       | Number        | Duration of the integration event in ms                        |   X   |          |
| ipAddress      | String        | Source IP address                                              |   X   |          |
| userAgent      | String        | User agent of the request                                      |         |          |
| metadata       | Object        | Additional contextual information                              |         |          |
| createdAt      | Date          | Log entry creation timestamp                                   |   X   |    X     |
| updatedAt      | Date          | Last update timestamp                                          |   X   |    X     |

## Request Data Sub-document Schema

```json
{
  "method": String,
  "url": String,
  "headers": Object,
  "body": Mixed,
  "queryParams": Object,
  "timestamp": Date
}
```

## Response Data Sub-document Schema

```json
{
  "statusCode": Number,
  "headers": Object,
  "body": Mixed,
  "timestamp": Date
}
```

## Error Sub-document Schema

```json
{
  "code": String,
  "message": String,
  "stack": String,
  "details": Object,
  "timestamp": Date
}
```

## Metadata Sub-document Schema

```json
{
  "environment": String,
  "version": String,
  "correlationId": String,
  "userId": ObjectId,
  "tags": [String],
  "customData": Object
}
```

## Indexing Strategy

* **Compound index:** `integrationType`, `status` (for monitoring)
* **Compound index:** `integrationId`, `createdAt` (for integration history)
* **Compound index:** `eventType`, `status` (for event analysis)
* **TTL index:** `createdAt` (for log rotation, e.g., 30 days)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "integrationType": "webhook",
  "integrationId": ObjectId("..."),
  "eventType": "job.posted",
  "status": "success",
  "direction": "outbound",
  "requestData": {
    "method": "POST",
    "url": "https://api.partner.com/webhooks/jobs",
    "headers": {
      "Content-Type": "application/json",
      "X-Webhook-Signature": "sha256=...",
      "User-Agent": "JobsPortal-Webhook/1.0"
    },
    "body": {
      "jobId": "12345",
      "title": "Senior Software Engineer",
      "company": "Tech Corp",
      "event": "job.posted"
    },
    "queryParams": {},
    "timestamp": ISODate("2024-03-20T15:30:00Z")
  },
  "responseData": {
    "statusCode": 200,
    "headers": {
      "Content-Type": "application/json"
    },
    "body": {
      "status": "received",
      "messageId": "abc123"
    },
    "timestamp": ISODate("2024-03-20T15:30:01Z")
  },
  "error": null,
  "duration": 985,
  "ipAddress": "***********",
  "userAgent": "Partner-Integration/2.0",
  "metadata": {
    "environment": "production",
    "version": "1.0.0",
    "correlationId": "req_xyz789",
    "userId": ObjectId("..."),
    "tags": [
      "job-distribution",
      "partner-sync"
    ],
    "customData": {
      "partnerName": "JobBoard",
      "region": "NA",
      "priority": "high"
    }
  },
  "createdAt": ISODate("2024-03-20T15:30:01Z"),
  "updatedAt": ISODate("2024-03-20T15:30:01Z")
}