# Collection: company_reviews

## Description

Stores company reviews and ratings submitted by users.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the review                               |   X   |    X     |
| companyId      | ObjectId      | Reference to the company                                       |   X   |    X     |
| reviewerId     | ObjectId      | Reference to the reviewer                                      |   X   |    X     |
| rating         | Number        | Overall rating (1-5)                                           |   X   |    X     |
| title          | String        | Review title                                                   |         |    X     |
| content        | String        | Review content                                                 |         |    X     |
| pros           | String        | Positive aspects                                               |         |    X     |
| cons           | String        | Negative aspects                                               |         |    X     |
| advice         | String        | Advice to management                                           |         |          |
| detailedRatings| Object        | Category-specific ratings                                      |         |    X     |
| status         | String        | Review status (pending, approved, rejected)                    |   X   |    X     |
| isVerified     | Boolean       | Employment verification status                                 |   X   |          |
| isAnonymous    | Boolean       | Anonymous review flag                                          |         |    X     |
| employmentStatus| String       | Current or former employee                                     |         |    X     |
| jobTitle       | String        | Reviewer's job title                                           |   X   |    X     |
| location       | String        | Work location                                                  |   X   |          |
| employmentDates| Object        | Employment date range                                          |         |          |
| helpfulVotes   | Number        | Number of helpful votes                                        |   X   |          |
| reportCount    | Number        | Number of reports/flags                                        |   X   |          |
| createdAt      | Date          | Review creation timestamp                                      |   X   |    X     |
| updatedAt      | Date          | Last review update timestamp                                   |   X   |    X     |

## Detailed Ratings Sub-document Schema

```json
{
  "workLifeBalance": Number,
  "compensation": Number,
  "careerGrowth": Number,
  "management": Number,
  "culture": Number,
  "benefits": Number,
  "jobSecurity": Number,
  "workEnvironment": Number
}
```

## Employment Dates Sub-document Schema

```json
{
  "startDate": Date,
  "endDate": Date,
  "totalDuration": String
}
```

## Indexing Strategy

* **Compound index:** `companyId`, `status`, `createdAt` (for company reviews)
* **Compound index:** `rating`, `helpfulVotes` (for top reviews)
* **Text index:** `title`, `content`, `pros`, `cons` (for review search)
* **Compound index:** `reviewerId`, `isAnonymous` (for user's reviews)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "companyId": ObjectId("..."),
  "reviewerId": ObjectId("..."),
  "rating": 4,
  "title": "Great work culture but room for improvement",
  "content": "Overall positive experience working here...",
  "pros": "Strong team collaboration, good benefits, flexible hours",
  "cons": "Limited career growth opportunities, slow decision making",
  "advice": "Consider implementing clearer career progression paths",
  "detailedRatings": {
    "workLifeBalance": 5,
    "compensation": 4,
    "careerGrowth": 3,
    "management": 4,
    "culture": 5,
    "benefits": 4,
    "jobSecurity": 4,
    "workEnvironment": 5
  },
  "status": "approved",
  "isVerified": true,
  "isAnonymous": false,
  "employmentStatus": "current",
  "jobTitle": "Senior Software Engineer",
  "location": "San Francisco, CA",
  "employmentDates": {
    "startDate": ISODate("2022-01-15"),
    "endDate": null,
    "totalDuration": "2 years 2 months"
  },
  "helpfulVotes": 45,
  "reportCount": 0,
  "createdAt": ISODate("2024-03-20"),
  "updatedAt": ISODate("2024-03-20")
}
``` 