# Collection: application_ratings

## Description

Stores ratings and evaluations for job applications.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the rating                               |   X   |    X     |
| applicationId  | ObjectId      | Reference to the application                                   |   X   |    X     |
| raterId        | ObjectId      | ID of user who provided the rating                            |   X   |    X     |
| overallRating  | Number        | Overall rating (1-5)                                          |   X   |    X     |
| criteria       | [Object]      | Ratings for specific criteria                                  |         |    X     |
| feedback       | String        | Detailed feedback                                              |         |          |
| stage          | String        | Application stage when rated                                   |   X   |    X     |
| recommendation | String        | Recommendation (reject, consider, strong_yes)                  |   X   |    X     |
| isConfidential | Boolean       | Whether the rating is confidential                             |         |          |
| metadata       | Object        | Additional rating context                                      |         |          |
| createdAt      | Date          | Rating creation timestamp                                      |   X   |    X     |
| updatedAt      | Date          | Last rating update timestamp                                   |   X   |    X     |

## Rating Criteria Sub-document Schema

```json
{
  "criterionName": String,
  "score": Number,
  "weight": Number,
  "comments": String
}
```

## Indexing Strategy

* **Compound index:** `applicationId`, `createdAt` (for rating history)
* **Compound index:** `raterId`, `stage` (for rater's evaluations)
* **Compound index:** `overallRating`, `recommendation` (for filtering top candidates)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "applicationId": ObjectId("..."),
  "raterId": ObjectId("..."),
  "overallRating": 4.5,
  "criteria": [
    {
      "criterionName": "Technical Skills",
      "score": 5,
      "weight": 0.4,
      "comments": "Strong technical background"
    },
    {
      "criterionName": "Communication",
      "score": 4,
      "weight": 0.3,
      "comments": "Clear and professional communication"
    },
    {
      "criterionName": "Culture Fit",
      "score": 4,
      "weight": 0.3,
      "comments": "Aligns well with company values"
    }
  ],
  "feedback": "Strong candidate with excellent technical skills and good cultural fit",
  "stage": "technical_interview",
  "recommendation": "strong_yes",
  "isConfidential": false,
  "metadata": {
    "interviewDate": ISODate("2024-03-19"),
    "interviewType": "technical",
    "interviewer": "Senior Developer"
  },
  "createdAt": ISODate("2024-03-20T10:00:00Z"),
  "updatedAt": ISODate("2024-03-20T10:00:00Z")
}
``` 