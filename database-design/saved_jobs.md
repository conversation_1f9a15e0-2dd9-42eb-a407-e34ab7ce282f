# Collection: saved_jobs

## Description

Allows job seekers to save job postings for later viewing.

## Schema

| Field        | Type      | Description                                                     | Indexed | Required |
| ------------ | --------- | --------------------------------------------------------------- | :-----: | :------: |
| \_id        | ObjectId  | Unique identifier for the saved job entry.                      |   X   |    X     |
| jobSeekerId  | ObjectId  | Reference to the job seeker who saved the job (`users` collection). |   X   |    X     |
| jobPostingId | ObjectId  | Reference to the saved job posting (`job_postings` collection). |   X   |    X     |
| savedAt      | Date      | Timestamp when the job was saved.                               |   X   |    X     |

## Indexing Strategy

*   **Compound index:** `jobSeekerId`, `jobPostingId` (to prevent duplicate saves and for efficient fetching of a user's saved jobs).