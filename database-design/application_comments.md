# Collection: application_comments

## Description

Stores internal comments and feedback on job applications.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the comment                              |   X   |    X     |
| applicationId  | ObjectId      | Reference to the application                                   |   X   |    X     |
| commenterId    | ObjectId      | ID of user who made the comment                               |   X   |    X     |
| comment        | String        | Comment text                                                   |         |    X     |
| visibility     | String        | Visibility level (team, private, all)                          |   X   |    X     |
| category       | String        | Comment category (feedback, interview_notes, general)          |   X   |          |
| priority       | String        | Priority level (high, medium, low)                             |   X   |          |
| tags           | [String]      | Relevant tags                                                  |         |          |
| parentCommentId| ObjectId      | Reference to parent comment if it's a reply                    |   X   |          |
| mentions       | [ObjectId]    | Users mentioned in the comment                                 |         |          |
| attachments    | [Object]      | Attached files                                                 |         |          |
| createdAt      | Date          | Comment creation timestamp                                     |   X   |    X     |
| updatedAt      | Date          | Last comment update timestamp                                  |   X   |    X     |

## Attachment Sub-document Schema

```json
{
  "fileId": String,
  "fileName": String,
  "fileType": String,
  "fileUrl": String,
  "uploadedAt": Date
}
```

## Indexing Strategy

* **Compound index:** `applicationId`, `createdAt` (for comment history)
* **Compound index:** `commenterId`, `visibility` (for user's comments)
* **Compound index:** `category`, `priority` (for filtering important comments)
* **Text index:** `comment` (for comment search)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "applicationId": ObjectId("..."),
  "commenterId": ObjectId("..."),
  "comment": "Candidate showed strong problem-solving skills during the technical interview",
  "visibility": "team",
  "category": "interview_notes",
  "priority": "high",
  "tags": ["technical", "positive"],
  "parentCommentId": null,
  "mentions": [ObjectId("...")],
  "attachments": [
    {
      "fileId": "file123",
      "fileName": "TechnicalAssessment.pdf",
      "fileType": "application/pdf",
      "fileUrl": "https://storage.example.com/files/assessment_123.pdf",
      "uploadedAt": ISODate("2024-03-20T10:00:00Z")
    }
  ],
  "createdAt": ISODate("2024-03-20T10:00:00Z"),
  "updatedAt": ISODate("2024-03-20T10:00:00Z")
}
``` 