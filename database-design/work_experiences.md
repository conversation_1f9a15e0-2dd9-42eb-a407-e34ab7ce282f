# Collection: work_experiences

## Description

Stores work experience entries for job seekers' resumes.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the work experience                      |   X   |    X     |
| resumeId       | ObjectId      | Reference to the resume                                        |   X   |    X     |
| companyName    | String        | Name of the company                                           |   X   |    X     |
| jobTitle       | String        | Job title                                                     |   X   |    X     |
| startDate      | Date          | Start date of employment                                      |   X   |    X     |
| endDate        | Date          | End date of employment (null if current)                      |   X   |          |
| isCurrent      | Boolean       | Whether this is the current job                               |   X   |          |
| location       | String        | Location of the job                                           |   X   |          |
| description    | String        | Description of responsibilities                               |         |    X     |
| achievements   | [String]      | List of key achievements                                      |         |          |
| skills         | [String]      | Skills used in this role                                     |   X   |          |
| createdAt      | Date          | Entry creation timestamp                                      |         |    X     |
| updatedAt      | Date          | Last entry update timestamp                                   |         |    X     |

## Indexing Strategy

* **Compound index:** `resumeId`, `startDate` (for chronological ordering)
* **Text index:** `companyName`, `jobTitle`, `description` (for experience search)
* **Compound index:** `companyName`, `location` (for company-based queries)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "resumeId": ObjectId("..."),
  "companyName": "Tech Corp Inc.",
  "jobTitle": "Senior Software Engineer",
  "startDate": ISODate("2020-01-15"),
  "endDate": null,
  "isCurrent": true,
  "location": "San Francisco, CA",
  "description": "Leading development of cloud-native applications...",
  "achievements": [
    "Reduced system latency by 40%",
    "Led team of 5 developers"
  ],
  "skills": ["Node.js", "MongoDB", "AWS"],
  "createdAt": ISODate("2024-03-20"),
  "updatedAt": ISODate("2024-03-20")
}
``` 