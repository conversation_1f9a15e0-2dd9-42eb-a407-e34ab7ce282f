# Collection: audit_events

## Description

Tracks security, compliance, and system-level events for auditing purposes.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the audit event                          |   X   |    X     |
| eventType      | String        | Type of audit event                                           |   X   |    X     |
| category       | String        | Event category (security, compliance, system)                  |   X   |    X     |
| severity       | String        | Event severity (info, warning, critical)                       |   X   |    X     |
| actor          | Object        | Entity that triggered the event                                |   X   |    X     |
| target         | Object        | Entity affected by the event                                   |   X   |    X     |
| action         | String        | Action performed                                               |   X   |    X     |
| status         | String        | Event status (success, failure)                                |   X   |    X     |
| details        | Object        | Detailed event information                                     |         |          |
| context        | Object        | Environmental context                                          |         |          |
| metadata       | Object        | Additional event metadata                                      |         |          |
| ipAddress      | String        | Source IP address                                              |   X   |    X     |
| userAgent      | String        | User agent information                                         |         |          |
| createdAt      | Date          | Event creation timestamp                                       |   X   |    X     |

## Actor Sub-document Schema

```json
{
  "id": Mixed,
  "type": String,
  "name": String,
  "role": String,
  "permissions": [String]
}
```

## Target Sub-document Schema

```json
{
  "id": Mixed,
  "type": String,
  "name": String,
  "resource": String,
  "attributes": Object
}
```

## Details Sub-document Schema

```json
{
  "description": String,
  "changes": {
    "before": Mixed,
    "after": Mixed
  },
  "reason": String,
  "notes": String,
  "relatedEvents": [ObjectId]
}
```

## Context Sub-document Schema

```json
{
  "environment": String,
  "component": String,
  "session": {
    "id": String,
    "type": String
  },
  "request": {
    "id": String,
    "method": String,
    "url": String
  }
}
```

## Indexing Strategy

* **Compound index:** `eventType`, `severity` (for event monitoring)
* **Compound index:** `category`, `createdAt` (for compliance reporting)
* **Compound index:** `actor.id`, `createdAt` (for user activity auditing)
* **Compound index:** `target.type`, `target.id` (for resource auditing)
* **TTL index:** `createdAt` (for retention policy, e.g., 2 years)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "eventType": "user.permission_change",
  "category": "security",
  "severity": "high",
  "actor": {
    "id": ObjectId("..."),
    "type": "user",
    "name": "John Admin",
    "role": "system_administrator",
    "permissions": ["manage_users", "manage_roles"]
  },
  "target": {
    "id": ObjectId("..."),
    "type": "user",
    "name": "Jane Doe",
    "resource": "role_assignment",
    "attributes": {
      "department": "HR",
      "location": "New York"
    }
  },
  "action": "update_permissions",
  "status": "success",
  "details": {
    "description": "Modified user role permissions",
    "changes": {
      "before": {
        "role": "recruiter",
        "permissions": ["view_jobs", "post_jobs"]
      },
      "after": {
        "role": "senior_recruiter",
        "permissions": ["view_jobs", "post_jobs", "manage_jobs"]
      }
    },
    "reason": "Promotion to Senior Recruiter",
    "notes": "Approved by HR Director",
    "relatedEvents": [
      ObjectId("..."),
      ObjectId("...")
    ]
  },
  "context": {
    "environment": "production",
    "component": "user_management",
    "session": {
      "id": "sess_abc123",
      "type": "web"
    },
    "request": {
      "id": "req_xyz789",
      "method": "PUT",
      "url": "/api/v1/users/permissions"
    }
  },
  "metadata": {
    "workflow": "user_promotion",
    "ticketId": "INC123456",
    "compliance": {
      "policy": "RBAC-001",
      "framework": "SOC2"
    }
  },
  "ipAddress": "***********",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) ...",
  "createdAt": ISODate("2024-03-20T15:30:00Z")
}