# Collection: rate_limits

## Description

Manages API rate limiting and usage tracking for different clients and endpoints.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the rate limit entry                     |   X   |    X     |
| key            | String        | Rate limit key (client:endpoint)                               |   X   |    X     |
| type           | String        | Limit type (ip, user, api_key, etc)                           |   X   |    X     |
| identifier     | String        | Client identifier (IP, user ID, API key)                       |   X   |    X     |
| endpoint       | String        | API endpoint or pattern                                        |   X   |    X     |
| limit          | Number        | Maximum requests allowed                                       |         |    X     |
| window         | Number        | Time window in seconds                                         |         |    X     |
| current        | Number        | Current request count                                          |   X   |    X     |
| resetAt        | Date          | When the current count resets                                  |   X   |    X     |
| overrides      | Object        | Custom limit overrides                                         |         |          |
| quotas         | Object        | Additional quota constraints                                   |         |          |
| status         | String        | Rate limit status (active, blocked)                            |   X   |    X     |
| metadata       | Object        | Additional rate limit metadata                                 |         |          |
| createdAt      | Date          | Entry creation timestamp                                       |   X   |    X     |
| updatedAt      | Date          | Last update timestamp                                          |   X   |    X     |

## Overrides Sub-document Schema

```json
{
  "burst": {
    "limit": Number,
    "window": Number
  },
  "throttle": {
    "rate": Number,
    "period": String
  },
  "expiry": Date
}
```

## Quotas Sub-document Schema

```json
{
  "daily": {
    "limit": Number,
    "used": Number,
    "resetAt": Date
  },
  "monthly": {
    "limit": Number,
    "used": Number,
    "resetAt": Date
  },
  "concurrent": {
    "limit": Number,
    "current": Number
  }
}
```

## Metadata Sub-document Schema

```json
{
  "plan": String,
  "tier": String,
  "reason": String,
  "notes": String,
  "lastViolation": Date,
  "violationCount": Number
}
```

## Indexing Strategy

* **Unique index:** `key` (for rate limit lookup)
* **Compound index:** `identifier`, `endpoint` (for client tracking)
* **Compound index:** `type`, `status` (for limit management)
* **TTL index:** `resetAt` (for automatic cleanup)

## Sample Documents

```json
{
  "_id": ObjectId("..."),
  "key": "api_key_123:job_search",
  "type": "api_key",
  "identifier": "api_key_123",
  "endpoint": "/api/v1/jobs/search",
  "limit": 1000,
  "window": 3600,
  "current": 150,
  "resetAt": ISODate("2024-03-20T16:30:00Z"),
  "overrides": {
    "burst": {
      "limit": 50,
      "window": 60
    },
    "throttle": {
      "rate": 10,
      "period": "second"
    },
    "expiry": ISODate("2024-12-31")
  },
  "quotas": {
    "daily": {
      "limit": 10000,
      "used": 5000,
      "resetAt": ISODate("2024-03-21")
    },
    "monthly": {
      "limit": 200000,
      "used": 75000,
      "resetAt": ISODate("2024-04-01")
    },
    "concurrent": {
      "limit": 5,
      "current": 2
    }
  },
  "status": "active",
  "metadata": {
    "plan": "enterprise",
    "tier": "gold",
    "reason": "high_volume_client",
    "notes": "Special rate limits for enterprise client",
    "lastViolation": null,
    "violationCount": 0
  },
  "createdAt": ISODate("2024-01-01"),
  "updatedAt": ISODate("2024-03-20T15:30:00Z")
}
```

```json
{
  "_id": ObjectId("..."),
  "key": "ip_***********:job_apply",
  "type": "ip",
  "identifier": "***********",
  "endpoint": "/api/v1/jobs/apply",
  "limit": 10,
  "window": 3600,
  "current": 8,
  "resetAt": ISODate("2024-03-20T16:30:00Z"),
  "overrides": null,
  "quotas": {
    "daily": {
      "limit": 50,
      "used": 25,
      "resetAt": ISODate("2024-03-21")
    },
    "concurrent": {
      "limit": 1,
      "current": 0
    }
  },
  "status": "active",
  "metadata": {
    "plan": "free",
    "tier": "basic",
    "reason": "abuse_prevention",
    "notes": "Standard IP-based rate limiting",
    "lastViolation": ISODate("2024-03-19"),
    "violationCount": 1
  },
  "createdAt": ISODate("2024-03-20T10:00:00Z"),
  "updatedAt": ISODate("2024-03-20T15:30:00Z")
}
```

``` 