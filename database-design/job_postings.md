# Collection: job_postings

## Description

Stores job postings created by employers. Enhanced with screening questions and detailed job requirements.

## Schema

| Field              | Type          | Description                                                              | Indexed | Required |
| ----------------- | ------------- | ------------------------------------------------------------------------ | :-----: | :------: |
| _id               | ObjectId      | Unique identifier for the job posting                                    |   X   |    X     |
| employerId        | ObjectId      | Reference to the employer (`users` collection)                          |   X   |    X     |
| companyId         | ObjectId      | Reference to the company (`companies` collection)                       |   X   |    X     |
| title             | String        | Job title                                                               |   X   |    X     |
| department        | String        | Department name                                                         |         |    X     |
| description       | String        | Detailed job description                                                |         |    X     |
| requirements      | [String]      | List of job requirements                                                |         |    X     |
| qualifications    | [String]      | Required qualifications                                                 |         |    X     |
| skills            | [String]      | Required skills                                                         |   X   |    X     |
| experienceLevel   | String        | Required experience level                                               |   X   |    X     |
| employmentType    | String        | Type of employment (full-time, part-time, etc.)                        |   X   |    X     |
| workType          | String        | Work arrangement (remote, onsite, hybrid)                              |   X   |    X     |
| location          | String        | Job location                                                            |   X   |    X     |
| salary            | Object        | {min: Number, max: Number, currency: String, period: String}           |   X   |          |
| benefits          | [String]      | List of job benefits                                                    |         |          |
| screeningQuestions| [Object]      | Array of screening questions with expected answers                      |         |          |
| requiredDocuments | [String]      | List of required application documents                                  |         |          |
| status            | String        | Status of posting (draft, published, closed)                           |   X   |    X     |
| approvalStatus    | String        | Approval status (pending, approved, rejected)                          |   X   |    X     |
| requiresModeration| Boolean       | Whether the posting needs moderation                                    |   X   |          |
| moderationStatus  | String        | Status of moderation                                                    |   X   |          |
| createdAt         | Date          | Job posting creation timestamp                                          |   X   |    X     |
| updatedAt         | Date          | Last job posting update timestamp                                       |   X   |    X     |
| expiresAt         | Date          | Job posting expiration date                                             |   X   |    X     |
| views             | Number        | Number of views                                                         |         |          |
| applications      | Number        | Number of applications received                                         |         |          |

## Indexing Strategy

* **Text index:** `title`, `description`, `skills`, `requirements` (for keyword-based job searches)
* **Compound index:** `status`, `approvalStatus`, `createdAt` (for filtering active and approved jobs)
* **Compound index:** `employerId`, `status`, `createdAt` (for employer's job listings)
* **Compound index:** `location`, `employmentType`, `workType` (for job search filtering)
* **TTL index:** `expiresAt` (to automatically expire job postings)

## Screening Question Sub-document Schema