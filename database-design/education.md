# Collection: education

## Description

Stores educational background information for job seekers' resumes.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the education entry                      |   X   |    X     |
| resumeId       | ObjectId      | Reference to the resume                                        |   X   |    X     |
| institution    | String        | Name of educational institution                                |   X   |    X     |
| location       | String        | Location of institution                                        |   X   |          |
| degree         | String        | Degree obtained                                                |   X   |    X     |
| fieldOfStudy   | String        | Field of study/major                                          |   X   |    X     |
| startDate      | Date          | Start date of education                                        |   X   |    X     |
| endDate        | Date          | End date of education (or expected)                           |   X   |          |
| isCurrentlyEnrolled | Boolean  | Current enrollment status                                      |   X   |          |
| grade          | String        | Grade/GPA achieved                                             |         |          |
| activities     | [String]      | Extracurricular activities                                     |         |          |
| achievements   | [String]      | Academic achievements                                          |         |          |
| description    | String        | Additional description                                         |         |          |
| createdAt      | Date          | Entry creation timestamp                                       |         |    X     |
| updatedAt      | Date          | Last entry update timestamp                                    |         |    X     |

## Indexing Strategy

* **Compound index:** `resumeId`, `startDate` (for chronological ordering)
* **Text index:** `institution`, `degree`, `fieldOfStudy` (for education search)
* **Compound index:** `isCurrentlyEnrolled`, `endDate` (for current education queries)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "resumeId": ObjectId("..."),
  "institution": "Stanford University",
  "location": "Stanford, CA",
  "degree": "Master of Science",
  "fieldOfStudy": "Computer Science",
  "startDate": ISODate("2018-09-01"),
  "endDate": ISODate("2020-06-15"),
  "isCurrentlyEnrolled": false,
  "grade": "3.8/4.0",
  "activities": [
    "ACM Student Chapter",
    "Robotics Club"
  ],
  "achievements": [
    "Dean's List 2019-2020",
    "Best Graduate Research Paper"
  ],
  "description": "Specialized in Machine Learning and Distributed Systems",
  "createdAt": ISODate("2024-03-20"),
  "updatedAt": ISODate("2024-03-20")
}
``` 