# Collection: job_search_history

## Description

Tracks and analyzes user's job search patterns and preferences.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the search                               |   X   |    X     |
| userId         | ObjectId      | Reference to the user                                          |   X   |    X     |
| searchTimestamp| Date          | Search timestamp                                               |   X   |    X     |
| sessionId      | String        | Search session ID                                              |   X   |          |
| searchType     | String        | Type of search (keyword, filter, browse)                       |   X   |          |
| keywords       | [String]      | Search keywords used                                           |   X   |          |
| filters        | Object        | Applied search filters                                         |         |          |
| location       | String        | Location filter                                                |   X   |          |
| jobType        | [String]      | Job types selected                                            |   X   |          |
| salaryRange    | Object        | Salary range filter                                           |         |          |
| experienceLevel| String        | Experience level filter                                        |   X   |          |
| remotePreference| String       | Remote work preference                                         |   X   |          |
| resultCount    | Number        | Number of results returned                                     |         |          |
| viewedJobs     | [ObjectId]    | Jobs viewed in this search                                    |         |          |
| appliedJobs    | [ObjectId]    | Jobs applied to from this search                              |         |          |
| savedJobs      | [ObjectId]    | Jobs saved from this search                                   |         |          |
| relevanceScore | Number        | Search relevance score                                         |   X   |          |
| metadata       | Object        | Additional search context                                      |         |          |

## Filter Sub-document Schema

```json
{
  "industry": [String],
  "companySize": [String],
  "companyType": [String],
  "postedDate": String,
  "skills": [String],
  "education": [String],
  "benefits": [String]
}
```

## Indexing Strategy

* **Compound index:** `userId`, `searchTimestamp` (for user's search history)
* **Compound index:** `keywords`, `location` (for search pattern analysis)
* **Text index:** `keywords` (for keyword analysis)
* **Compound index:** `sessionId`, `searchTimestamp` (for session analysis)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "userId": ObjectId("..."),
  "searchTimestamp": ISODate("2024-03-20T10:00:00Z"),
  "sessionId": "sess_123456",
  "searchType": "filter",
  "keywords": ["software engineer", "full stack", "react"],
  "filters": {
    "industry": ["Technology", "Finance"],
    "companySize": ["51-200", "201-500"],
    "companyType": ["Startup", "Enterprise"],
    "postedDate": "past_week",
    "skills": ["React", "Node.js", "MongoDB"],
    "education": ["Bachelor's", "Master's"],
    "benefits": ["Health Insurance", "Remote Work"]
  },
  "location": "San Francisco, CA",
  "jobType": ["full-time", "contract"],
  "salaryRange": {
    "min": 120000,
    "max": 180000,
    "currency": "USD"
  },
  "experienceLevel": "senior",
  "remotePreference": "hybrid",
  "resultCount": 45,
  "viewedJobs": [
    ObjectId("..."),
    ObjectId("...")
  ],
  "appliedJobs": [
    ObjectId("...")
  ],
  "savedJobs": [
    ObjectId("..."),
    ObjectId("...")
  ],
  "relevanceScore": 0.85,
  "metadata": {
    "deviceType": "desktop",
    "browser": "Chrome",
    "searchDuration": 180,
    "filtersApplied": 6
  }
}
``` 