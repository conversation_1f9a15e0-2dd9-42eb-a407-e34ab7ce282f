# Collection: email_templates

## Description

Stores customizable email templates for various system communications.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the template                             |   X   |    X     |
| name           | String        | Template name                                                  |   X   |    X     |
| code           | String        | Unique template identifier code                                |   X   |    X     |
| type           | String        | Template type (notification, marketing, transactional)         |   X   |    X     |
| subject        | String        | Email subject line                                             |         |    X     |
| htmlContent    | String        | HTML version of the template                                   |         |    X     |
| textContent    | String        | Plain text version of the template                             |         |    X     |
| variables      | [String]      | List of template variables                                     |         |    X     |
| language       | String        | Template language code                                         |   X   |    X     |
| category       | String        | Template category                                              |   X   |          |
| sender         | Object        | Sender information                                             |         |          |
| isActive       | Boolean       | Whether the template is active                                 |   X   |    X     |
| version        | Number        | Template version                                               |   X   |    X     |
| metadata       | Object        | Additional template metadata                                    |         |          |
| createdAt      | Date          | Template creation timestamp                                    |   X   |    X     |
| updatedAt      | Date          | Last update timestamp                                          |   X   |    X     |

## Sender Sub-document Schema

```json
{
  "name": String,
  "email": String,
  "replyTo": String
}
```

## Metadata Sub-document Schema

```json
{
  "description": String,
  "previewText": String,
  "attachments": [{
    "name": String,
    "type": String,
    "url": String
  }],
  "defaultLocale": String,
  "lastTestedDate": Date,
  "testResults": {
    "spamScore": Number,
    "renderTests": [String],
    "deliverabilityScore": Number
  }
}
```

## Indexing Strategy

* **Unique index:** `code`, `language`, `version` (for unique template versions)
* **Compound index:** `type`, `category`, `isActive` (for template filtering)
* **Text index:** `name`, `subject`, `htmlContent` (for template search)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "name": "Job Application Confirmation",
  "code": "JOB_APPLICATION_CONFIRM",
  "type": "transactional",
  "subject": "Application Received: {{jobTitle}}",
  "htmlContent": "<div><h1>Thank you for applying, {{userName}}!</h1>...</div>",
  "textContent": "Thank you for applying, {{userName}}!...",
  "variables": [
    "userName",
    "jobTitle",
    "companyName",
    "applicationId",
    "applicationDate"
  ],
  "language": "en-US",
  "category": "applications",
  "sender": {
    "name": "{{companyName}} Careers",
    "email": "careers@{{companyDomain}}",
    "replyTo": "no-reply@{{companyDomain}}"
  },
  "isActive": true,
  "version": 2,
  "metadata": {
    "description": "Confirmation email sent when a candidate submits a job application",
    "previewText": "Thank you for applying to {{jobTitle}}",
    "attachments": [
      {
        "name": "Application_Guide.pdf",
        "type": "application/pdf",
        "url": "https://storage.example.com/templates/guides/application.pdf"
      }
    ],
    "defaultLocale": "en-US",
    "lastTestedDate": ISODate("2024-03-15"),
    "testResults": {
      "spamScore": 0.1,
      "renderTests": ["gmail", "outlook", "apple-mail"],
      "deliverabilityScore": 0.98
    }
  },
  "createdAt": ISODate("2024-03-01"),
  "updatedAt": ISODate("2024-03-20")
}
``` 