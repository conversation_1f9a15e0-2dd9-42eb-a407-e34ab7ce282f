# Collection: user_activity_logs

## Description

Tracks detailed user activities for analytics and personalization.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the activity                             |   X   |    X     |
| userId         | ObjectId      | Reference to user                                              |   X   |    X     |
| activityType   | String        | Type of activity (profile_view, job_search, etc.)             |   X   |    X     |
| entityType     | String        | Type of entity involved (job, company, resume)                 |   X   |    X     |
| entityId       | ObjectId      | ID of the entity involved                                      |   X   |          |
| action         | String        | Specific action taken                                          |   X   |    X     |
| timestamp      | Date          | Activity timestamp                                             |   X   |    X     |
| sessionId      | String        | Session identifier                                             |   X   |          |
| platform       | String        | Platform used (web, mobile, api)                               |         |    X     |
| deviceInfo     | Object        | Device information                                             |         |          |
| location       | Object        | Geographic location                                            |         |          |
| metadata       | Object        | Additional activity context                                    |         |          |
| duration       | Number        | Duration of activity in seconds                                |         |          |
| status         | String        | Activity status (started, completed, failed)                   |   X   |          |
| errorDetails   | Object        | Error information if activity failed                           |         |          |

## Device Info Sub-document Schema

```json
{
  "type": String,
  "browser": String,
  "os": String,
  "deviceId": String,
  "isMobile": Boolean
}
```

## Location Sub-document Schema

```json
{
  "ip": String,
  "city": String,
  "country": String,
  "coordinates": {
    "latitude": Number,
    "longitude": Number
  }
}
```

## Indexing Strategy

* **Compound index:** `userId`, `timestamp` (for user activity history)
* **Compound index:** `activityType`, `entityType` (for activity analysis)
* **Compound index:** `sessionId`, `timestamp` (for session analysis)
* **TTL index:** `timestamp` (for data retention policy)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "userId": ObjectId("..."),
  "activityType": "job_search",
  "entityType": "job_posting",
  "entityId": ObjectId("..."),
  "action": "apply",
  "timestamp": ISODate("2024-03-20T10:30:00Z"),
  "sessionId": "sess_123456",
  "platform": "web",
  "deviceInfo": {
    "type": "desktop",
    "browser": "Chrome 122.0",
    "os": "Windows 11",
    "deviceId": "device_789",
    "isMobile": false
  },
  "location": {
    "ip": "***********",
    "city": "San Francisco",
    "country": "US",
    "coordinates": {
      "latitude": 37.7749,
      "longitude": -122.4194
    }
  },
  "metadata": {
    "searchQuery": "software engineer",
    "filters": {
      "location": "San Francisco",
      "jobType": "full-time"
    },
    "resultCount": 45
  },
  "duration": 300,
  "status": "completed"
}
```