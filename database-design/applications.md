# Collection: applications

## Description

Stores job applications with screening answers and evaluation details.

## Schema

| Field              | Type          | Description                                                    | Indexed | Required |
| ----------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id               | ObjectId      | Unique identifier for the application                          |   X   |    X     |
| jobPostingId      | ObjectId      | Reference to the job posting                                   |   X   |    X     |
| jobSeekerId       | ObjectId      | Reference to the job seeker                                    |   X   |    X     |
| resumeId          | ObjectId      | Reference to the submitted resume                              |   X   |    X     |
| coverLetter       | String        | Cover letter text                                              |         |          |
| typedCoverLetter  | String        | Cover letter typed directly in application                     |         |          |
| screeningAnswers  | [Object]      | Array of answers to screening questions                        |         |          |
| additionalDocs    | [Object]      | Additional uploaded documents                                  |         |          |
| hiringStage       | String        | Current stage (Applied, Shortlisted, Interviewing, etc.)       |   X   |    X     |
| status            | String        | Application status                                             |   X   |    X     |
| rating            | Number        | Employer's rating (1-5)                                        |   X   |          |
| lastStatusUpdate  | Date          | Date of last status change                                     |   X   |          |
| rejectionReason   | String        | Reason if rejected                                             |         |          |
| interviewNotes    | String        | Notes from interviews                                          |         |          |
| createdAt         | Date          | Application submission timestamp                               |   X   |    X     |
| updatedAt         | Date          | Last application update timestamp                              |   X   |    X     |

## Screening Answer Sub-document Schema

```json
{
  "questionId": ObjectId,
  "answer": String,
  "score": Number,
  "evaluatorNotes": String
}
```

## Additional Document Sub-document Schema

```json
{
  "type": String,
  "name": String,
  "url": String,
  "uploadedAt": Date
}
```

## Indexing Strategy

* **Compound index:** `jobPostingId`, `hiringStage` (for viewing applications by stage)
* **Compound index:** `jobSeekerId`, `createdAt` (for job seeker's application history)
* **Compound index:** `status`, `lastStatusUpdate` (for tracking application progress)
* **Compound index:** `rating`, `hiringStage` (for finding top candidates)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "jobPostingId": ObjectId("..."),
  "jobSeekerId": ObjectId("..."),
  "resumeId": ObjectId("..."),
  "coverLetter": "Dear Hiring Manager...",
  "screeningAnswers": [
    {
      "questionId": ObjectId("..."),
      "answer": "5+ years",
      "score": 5,
      "evaluatorNotes": "Excellent experience level"
    }
  ],
  "additionalDocs": [
    {
      "type": "portfolio",
      "name": "Portfolio.pdf",
      "url": "https://storage.example.com/...",
      "uploadedAt": ISODate("2024-03-20")
    }
  ],
  "hiringStage": "Shortlisted",
  "status": "under_review",
  "rating": 4,
  "lastStatusUpdate": ISODate("2024-03-21"),
  "createdAt": ISODate("2024-03-20"),
  "updatedAt": ISODate("2024-03-21")
}
```