# Collection: job_searches

## Description

Stores recent job searches performed by job seekers for analytics and recommendations.

## Schema

| Field        | Type      | Description                                                                   | Indexed | Required |
| ------------ | --------- | ----------------------------------------------------------------------------- | :-----: | :------: |
| \_id        | ObjectId  | Unique identifier for the job search.                                       |   X   |    X     |
| jobSeekerId  | ObjectId  | Reference to the job seeker who performed the search (`users` collection).   |   X   |    X     |
| keywords     | String    | Search keywords used.                                                         |   X   |          |
| filters      | Object    | Applied filters (location, job type, salary range, etc.) stored as a JSON object. |         |          |
| searchDate   | Date      | Timestamp of the job search.                                                  |   X   |    X     |

## Indexing Strategy

*   **Compound index:** `jobSeekerId`, `searchDate` (for fetching a user's recent searches).
*   **Text index:** `keywords` (for analyzing popular search terms).

## Sample Document (Illustrative - adapt based on your specific filters)

```json
{
  "location": "San Francisco, CA",
  "jobType": "full-time",
  "salaryMin": 80000,
  "salaryMax": 120000,
  "experienceLevel": "mid-level",
  "remote": false, 
  "datePosted": "past-week" 
}