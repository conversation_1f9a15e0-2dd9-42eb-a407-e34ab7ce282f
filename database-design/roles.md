# Collection: roles

## Description

Defines user roles and their associated permissions.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the role                                 |   X   |    X     |
| name           | String        | Role name                                                      |   X   |    X     |
| slug           | String        | URL-friendly version of name                                   |   X   |    X     |
| description    | String        | Role description                                               |         |    X     |
| permissions    | [String]      | Array of permission codes                                      |   X   |    X     |
| isSystem       | Boolean       | Whether it's a system role                                     |   X   |    X     |
| isActive       | Boolean       | Whether the role is active                                     |   X   |    X     |
| level          | Number        | Role hierarchy level                                           |   X   |    X     |
| scope          | Object        | Role scope limitations                                         |         |          |
| metadata       | Object        | Additional role metadata                                       |         |          |
| createdAt      | Date          | Role creation timestamp                                        |   X   |    X     |
| updatedAt      | Date          | Last update timestamp                                          |   X   |    X     |

## Scope Sub-document Schema

```json
{
  "organizations": [ObjectId],
  "departments": [String],
  "locations": [String],
  "features": [String]
}
```

## Metadata Sub-document Schema

```json
{
  "icon": String,
  "color": String,
  "maxUsers": Number,
  "customFields": Object,
  "auditLog": {
    "lastReviewed": Date,
    "reviewedBy": ObjectId,
    "notes": String
  }
}
```

## Indexing Strategy

* **Unique index:** `slug` (for unique role names)
* **Compound index:** `isSystem`, `isActive` (for active system roles)
* **Compound index:** `level`, `name` (for role hierarchy)
* **Text index:** `name`, `description` (for role search)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "name": "HR Manager",
  "slug": "hr-manager",
  "description": "Human Resources Manager with full hiring capabilities",
  "permissions": [
    "MANAGE_JOBS",
    "MANAGE_APPLICATIONS",
    "VIEW_ANALYTICS",
    "MANAGE_TEAM",
    "APPROVE_POSTINGS"
  ],
  "isSystem": true,
  "isActive": true,
  "level": 2,
  "scope": {
    "organizations": [ObjectId("...")],
    "departments": ["HR", "Recruitment"],
    "locations": ["ALL"],
    "features": ["hiring", "analytics", "reporting"]
  },
  "metadata": {
    "icon": "users",
    "color": "#2ecc71",
    "maxUsers": 10,
    "customFields": {
      "requiresTraining": true,
      "trainingModules": ["HR_101", "HIRING_BEST_PRACTICES"]
    },
    "auditLog": {
      "lastReviewed": ISODate("2024-03-15"),
      "reviewedBy": ObjectId("..."),
      "notes": "Annual role review completed"
    }
  },
  "createdAt": ISODate("2024-03-01"),
  "updatedAt": ISODate("2024-03-20")
}
``` 