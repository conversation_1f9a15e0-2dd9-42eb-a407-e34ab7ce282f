# Collection: system_metrics

## Description

Stores system performance, health metrics, and operational statistics.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the metric                               |   X   |    X     |
| name           | String        | Metric name                                                    |   X   |    X     |
| type           | String        | Metric type (counter, gauge, histogram)                        |   X   |    X     |
| value          | Number        | Metric value                                                   |   X   |    X     |
| unit           | String        | Unit of measurement                                            |         |    X     |
| timestamp      | Date          | Measurement timestamp                                          |   X   |    X     |
| interval       | String        | Collection interval (1m, 5m, 1h, etc)                         |   X   |    X     |
| component      | String        | System component being measured                                |   X   |    X     |
| tags           | Object        | Metric tags for filtering                                      |   X   |          |
| dimensions     | [String]      | Metric dimensions                                              |         |          |
| statistics     | Object        | Statistical aggregations                                       |         |          |
| metadata       | Object        | Additional metric metadata                                     |         |          |
| alerts         | [Object]      | Associated alert configurations                                |         |          |

## Tags Sub-document Schema

```json
{
  "environment": String,
  "region": String,
  "service": String,
  "instance": String,
  "version": String
}
```

## Statistics Sub-document Schema

```json
{
  "min": Number,
  "max": Number,
  "sum": Number,
  "count": Number,
  "avg": Number,
  "p50": Number,
  "p90": Number,
  "p95": Number,
  "p99": Number
}
```

## Alerts Sub-document Schema

```json
{
  "name": String,
  "condition": String,
  "threshold": Number,
  "duration": String,
  "severity": String,
  "status": String
}
```

## Indexing Strategy

* **Compound index:** `name`, `timestamp` (for time series queries)
* **Compound index:** `component`, `type` (for component metrics)
* **Compound index:** `tags.environment`, `tags.service` (for service metrics)
* **TTL index:** `timestamp` (for data retention, e.g., 30 days)

## Sample Documents

```json
{
  "_id": ObjectId("..."),
  "name": "api_response_time",
  "type": "histogram",
  "value": 245.5,
  "unit": "milliseconds",
  "timestamp": ISODate("2024-03-20T15:30:00Z"),
  "interval": "1m",
  "component": "job_search_api",
  "tags": {
    "environment": "production",
    "region": "us-east-1",
    "service": "search_service",
    "instance": "search-1",
    "version": "1.5.0"
  },
  "dimensions": [
    "endpoint",
    "method",
    "status_code"
  ],
  "statistics": {
    "min": 150.2,
    "max": 450.8,
    "sum": 24550.5,
    "count": 100,
    "avg": 245.5,
    "p50": 235.0,
    "p90": 350.0,
    "p95": 400.0,
    "p99": 445.0
  },
  "metadata": {
    "description": "API endpoint response time",
    "owner": "Platform Team",
    "slo": {
      "target": 300,
      "compliance": 0.99
    }
  },
  "alerts": [
    {
      "name": "High Latency Alert",
      "condition": "value > threshold for duration",
      "threshold": 400,
      "duration": "5m",
      "severity": "warning",
      "status": "inactive"
    }
  ]
}
```

```json
{
  "_id": ObjectId("..."),
  "name": "job_search_requests",
  "type": "counter",
  "value": 15234,
  "unit": "requests",
  "timestamp": ISODate("2024-03-20T15:30:00Z"),
  "interval": "5m",
  "component": "search_service",
  "tags": {
    "environment": "production",
    "region": "us-east-1",
    "service": "search_service",
    "instance": "search-1",
    "version": "1.5.0"
  },
  "dimensions": [
    "query_type",
    "filter_count",
    "result_size"
  ],
  "statistics": {
    "min": 10000,
    "max": 20000,
    "sum": 15234,
    "count": 1,
    "avg": 15234
  },
  "metadata": {
    "description": "Total job search requests",
    "owner": "Search Team",
    "notes": "Includes all search types"
  },
  "alerts": [
    {
      "name": "Low Traffic Alert",
      "condition": "value < threshold for duration",
      "threshold": 5000,
      "duration": "15m",
      "severity": "critical",
      "status": "inactive"
    }
  ]
}
``` 