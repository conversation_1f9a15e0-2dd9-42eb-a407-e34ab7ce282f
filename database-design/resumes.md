# Collection: resumes

## Description

Stores resumes uploaded by job seekers.

## Schema

| Field        | Type      | Description                                                              | Indexed | Required |
| ------------ | --------- | ------------------------------------------------------------------------ | :-----: | :------: |
| \_id        | ObjectId  | Unique identifier for the resume.                                        |   X   |    X     |
| jobSeekerId  | ObjectId  | Reference to the job seeker who owns the resume (`users` collection).    |   X   |    X     |
| format       | String    | Format of the resume file (e.g., "PDF", "DOCX").                        |         |    X     |
| template     | String    | Design template used (if applicable).                                   |         |          |
| content      | String    | Resume data (can be a URL to cloud storage or embedded binary data).     |         |    X     |
| parsedContent | String    |  Text extracted from the resume content for searching.                   |   X   |          |
| createdAt    | Date      | Resume upload timestamp.                                                 |         |    X     |
| updatedAt    | Date      | Last resume update timestamp.                                           |         |          |
| isPrimary    | Boolean   | Indicates if this is the job seeker's primary resume.                   |         |          |

## Indexing Strategy

*   **Text index:** `parsedContent` (for searching within resume content).
*   **Compound index:** `jobSeekerId`, `isPrimary` (for fetching a user's primary resume).