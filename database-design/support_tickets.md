
**9. support\_tickets.md**

```markdown
# Collection: support_tickets

## Description

Stores support tickets created by users.

## Schema

| Field             | Type      | Description                                                              | Indexed | Required |
| ----------------- | --------- | ------------------------------------------------------------------------ | :-----: | :------: |
| \_id             | ObjectId  | Unique identifier for the support ticket.                               |   X   |    X     |
| userId            | ObjectId  | Reference to the user who created the ticket (`users` collection).      |   X   |    X     |
| subject           | String    | Subject or brief description of the issue.                              |         |    X     |
| issueDescription  | String    | Detailed description of the support issue.                               |         |    X     |
| status            | String    | ("open", "in progress", "pending user response", "resolved", "closed"). |   X   |    X     |
| priority          | String    | ("low", "medium", "high", "urgent").                                     |   X   |          |
| category          | String    | Category of the support issue (e.g., "account", "payment", "technical"). |   X   |          |
| createdAt         | Date      | Ticket creation timestamp.                                                |   X   |    X     |
| updatedAt         | Date      | Last ticket update timestamp.                                            |         |    X     |
| assignedAdminId   | ObjectId  | Reference to the admin assigned to the ticket (`users` collection).   |   X   |          |
| closedAt          | Date      | Timestamp when the ticket was closed.                                   |         |          |

## Indexing Strategy

*   **Compound index:** `status`, `priority`, `createdAt` (for admin dashboards and ticket management).
*   **Compound index:** `userId`, `createdAt` (for user-specific ticket history).