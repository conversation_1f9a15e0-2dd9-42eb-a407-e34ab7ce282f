# Collection: data_exports

## Description

Manages data export jobs, configurations, and tracking for various data types.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the export                               |   X   |    X     |
| name           | String        | Export name/description                                        |   X   |    X     |
| type           | String        | Export type (jobs, applications, analytics)                    |   X   |    X     |
| format         | String        | Export format (csv, json, xlsx)                                |   X   |    X     |
| status         | String        | Export status (pending, processing, completed)                 |   X   |    X     |
| filters        | Object        | Data filtering criteria                                        |         |          |
| fields         | [String]      | Fields to include in export                                    |         |          |
| options        | Object        | Export configuration options                                   |         |          |
| schedule       | Object        | Scheduling information for recurring exports                   |         |          |
| destination    | Object        | Export destination configuration                               |         |    X     |
| progress       | Object        | Export progress information                                    |         |          |
| result         | Object        | Export result details                                          |         |          |
| metadata       | Object        | Additional export metadata                                     |         |          |
| requestedBy    | ObjectId      | Reference to user who requested the export                    |   X   |    X     |
| expiresAt      | Date          | Export data expiration timestamp                               |   X   |          |
| createdAt      | Date          | Export creation timestamp                                      |   X   |    X     |
| updatedAt      | Date          | Last update timestamp                                          |   X   |    X     |

## Filters Sub-document Schema

```json
{
  "conditions": [{
    "field": String,
    "operator": String,
    "value": Mixed
  }],
  "dateRange": {
    "start": Date,
    "end": Date
  },
  "limit": Number,
  "sort": Object
}
```

## Options Sub-document Schema

```json
{
  "compression": {
    "enabled": Boolean,
    "type": String
  },
  "encryption": {
    "enabled": Boolean,
    "algorithm": String,
    "keyId": String
  },
  "formatting": {
    "dateFormat": String,
    "numberFormat": String,
    "timezone": String,
    "locale": String
  },
  "chunking": {
    "enabled": Boolean,
    "size": Number
  }
}
```

## Destination Sub-document Schema

```json
{
  "type": String,
  "config": {
    "storage": {
      "provider": String,
      "bucket": String,
      "path": String
    },
    "notification": {
      "type": String,
      "target": String
    },
    "access": {
      "public": Boolean,
      "expiresIn": Number
    }
  }
}
```

## Progress Sub-document Schema

```json
{
  "current": Number,
  "total": Number,
  "percentage": Number,
  "startedAt": Date,
  "estimatedCompletion": Date,
  "status": {
    "phase": String,
    "message": String,
    "error": Object
  }
}
```

## Indexing Strategy

* **Compound index:** `type`, `status` (for export processing)
* **Compound index:** `requestedBy`, `createdAt` (for user exports)
* **TTL index:** `expiresAt` (for export cleanup)
* **Index:** `schedule.nextRun` (for scheduled exports)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "name": "Monthly Job Applications Report",
  "type": "applications",
  "format": "xlsx",
  "status": "processing",
  "filters": {
    "conditions": [
      {
        "field": "status",
        "operator": "in",
        "value": ["submitted", "reviewed", "rejected"]
      },
      {
        "field": "department",
        "operator": "equals",
        "value": "Engineering"
      }
    ],
    "dateRange": {
      "start": ISODate("2024-03-01"),
      "end": ISODate("2024-03-31")
    },
    "limit": 10000,
    "sort": {
      "submittedAt": -1
    }
  },
  "fields": [
    "applicationId",
    "jobTitle",
    "candidateName",
    "status",
    "submittedAt",
    "lastUpdated",
    "score",
    "source"
  ],
  "options": {
    "compression": {
      "enabled": true,
      "type": "zip"
    },
    "encryption": {
      "enabled": true,
      "algorithm": "AES-256",
      "keyId": "key_123"
    },
    "formatting": {
      "dateFormat": "YYYY-MM-DD HH:mm:ss",
      "numberFormat": "#,##0.00",
      "timezone": "America/New_York",
      "locale": "en-US"
    },
    "chunking": {
      "enabled": true,
      "size": 5000
    }
  },
  "schedule": {
    "type": "monthly",
    "dayOfMonth": 1,
    "time": "00:00",
    "timezone": "UTC",
    "nextRun": ISODate("2024-04-01")
  },
  "destination": {
    "type": "s3",
    "config": {
      "storage": {
        "provider": "aws",
        "bucket": "exports-bucket",
        "path": "reports/applications/2024-03"
      },
      "notification": {
        "type": "email",
        "target": "<EMAIL>"
      },
      "access": {
        "public": false,
        "expiresIn": 604800
      }
    }
  },
  "progress": {
    "current": 3500,
    "total": 7500,
    "percentage": 46.67,
    "startedAt": ISODate("2024-03-20T15:30:00Z"),
    "estimatedCompletion": ISODate("2024-03-20T15:35:00Z"),
    "status": {
      "phase": "processing",
      "message": "Exporting application records",
      "error": null
    }
  },
  "result": {
    "files": [
      {
        "name": "applications_part1.xlsx",
        "size": 2500000,
        "url": "https://exports-bucket.s3.amazonaws.com/...",
        "checksum": "sha256:abc123..."
      }
    ],
    "summary": {
      "totalRecords": 7500,
      "totalFiles": 2,
      "totalSize": 5000000,
      "processedAt": ISODate("2024-03-20T15:35:00Z")
    }
  },
  "metadata": {
    "requestId": "exp_123",
    "source": "scheduled_task",
    "priority": "normal",
    "retention": {
      "policy": "30_days",
      "reason": "compliance"
    },
    "tags": [
      "monthly_report",
      "applications",
      "engineering"
    ]
  },
  "requestedBy": ObjectId("..."),
  "expiresAt": ISODate("2024-04-19"),
  "createdAt": ISODate("2024-03-20T15:30:00Z"),
  "updatedAt": ISODate("2024-03-20T15:32:00Z")
}