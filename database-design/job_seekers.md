# Collection: job_seekers

## Description

Stores detailed profiles of job seekers with their preferences and visibility settings.

## Schema

| Field               | Type          | Description                                                    | Indexed | Required |
| ------------------ | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id                | ObjectId      | Unique identifier for the job seeker                           |   X   |    X     |
| userId             | ObjectId      | Reference to base user account                                 |   X   |    X     |
| profileVisibility  | String        | Profile visibility level ("Public", "Private", "Partial")      |   X   |    X     |
| firstName          | String        | First name                                                     |   X   |    X     |
| lastName           | String        | Last name                                                      |   X   |    X     |
| dateOfBirth        | Date          | Date of birth                                                  |         |          |
| currentLocation    | String        | Current location                                               |   X   |    X     |
| preferredLocations | [String]      | List of preferred work locations                               |   X   |          |
| profilePicture     | String        | URL to profile picture                                         |         |          |
| summary            | String        | Professional summary                                           |         |          |
| desiredJobTitles   | [String]      | Preferred job titles                                           |   X   |          |
| desiredSalary      | Object        | {min: Number, max: Number, currency: String, period: String}   |         |          |
| preferredWorkTypes | [String]      | Preferred work types (remote, onsite, hybrid)                  |   X   |          |
| preferredIndustries| [String]      | Preferred industries                                           |   X   |          |
| yearsOfExperience  | Number        | Total years of experience                                      |   X   |          |
| availableFrom      | Date          | Date available to start work                                   |         |          |
| sectionVisibility  | Object        | Visibility settings for different profile sections             |         |          |
| createdAt          | Date          | Profile creation timestamp                                     |   X   |    X     |
| updatedAt          | Date          | Last profile update timestamp                                  |   X   |    X     |

## Section Visibility Sub-document Schema

```json
{
  "education": String,      // "Public" or "Private"
  "experience": String,     // "Public" or "Private"
  "skills": String,        // "Public" or "Private"
  "contact": String,       // "Public" or "Private"
  "salary": String        // "Public" or "Private"
}
```

## Indexing Strategy

* **Text index:** `firstName`, `lastName`, `summary`, `preferredJobTitles` (for candidate search)
* **Compound index:** `profileVisibility`, `preferredLocations` (for location-based searches)
* **Compound index:** `yearsOfExperience`, `preferredIndustries` (for filtering candidates)
* **Compound index:** `userId`, `updatedAt` (for profile updates) 