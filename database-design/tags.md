# Collection: tags

## Description

Stores hierarchical job categories, skills, and other taxonomies used throughout the system.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the tag                                  |   X   |    X     |
| name           | String        | Tag name                                                       |   X   |    X     |
| slug           | String        | URL-friendly version of name                                   |   X   |    X     |
| type           | String        | Tag type (skill, industry, job_category, etc.)                |   X   |    X     |
| parentId       | ObjectId      | Reference to parent tag (for hierarchy)                        |   X   |          |
| level          | Number        | Hierarchy level (0 for root)                                   |   X   |    X     |
| description    | String        | Tag description                                                |         |          |
| synonyms       | [String]      | Alternative names/terms                                        |   X   |          |
| isActive       | Boolean       | Whether the tag is active                                      |   X   |    X     |
| priority       | Number        | Display/search priority                                        |   X   |          |
| metadata       | Object        | Additional tag metadata                                        |         |          |
| usageCount     | Number        | Number of times tag is used                                    |   X   |          |
| createdAt      | Date          | Tag creation timestamp                                         |   X   |    X     |
| updatedAt      | Date          | Last update timestamp                                          |   X   |    X     |

## Metadata Sub-document Schema

```json
{
  "icon": String,
  "color": String,
  "externalId": String,
  "source": String,
  "validationRules": {
    "format": String,
    "min": Number,
    "max": Number,
    "regex": String
  },
  "relatedTags": [ObjectId]
}
```

## Indexing Strategy

* **Unique index:** `slug`, `type` (for unique tag names within types)
* **Compound index:** `parentId`, `level` (for hierarchy navigation)
* **Compound index:** `type`, `isActive`, `priority` (for filtered tag lists)
* **Text index:** `name`, `synonyms`, `description` (for tag search)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "name": "Software Development",
  "slug": "software-development",
  "type": "job_category",
  "parentId": ObjectId("..."),
  "level": 1,
  "description": "Jobs related to software development and programming",
  "synonyms": [
    "Software Engineering",
    "Programming",
    "Software Design"
  ],
  "isActive": true,
  "priority": 100,
  "metadata": {
    "icon": "code",
    "color": "#3498db",
    "externalId": "CAT_001",
    "source": "internal",
    "validationRules": {
      "format": "string",
      "min": 2,
      "max": 50,
      "regex": "^[a-zA-Z0-9\\s-]+$"
    },
    "relatedTags": [
      ObjectId("..."),
      ObjectId("...")
    ]
  },
  "usageCount": 1250,
  "createdAt": ISODate("2024-03-01"),
  "updatedAt": ISODate("2024-03-20")
}
```