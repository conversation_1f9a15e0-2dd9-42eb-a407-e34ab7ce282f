# Collection: system_configurations

## Description

Stores global system configurations and settings with versioning support.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the configuration                        |   X   |    X     |
| key            | String        | Unique configuration key                                       |   X   |    X     |
| value          | Mixed         | Configuration value (can be any type)                          |         |    X     |
| type           | String        | Data type of the value                                        |         |    X     |
| category       | String        | Configuration category                                         |   X   |    X     |
| description    | String        | Description of the configuration                               |         |          |
| environment    | String        | Environment (development, staging, production)                 |   X   |    X     |
| isActive       | Boolean       | Whether the configuration is active                            |   X   |    X     |
| version        | Number        | Configuration version                                          |   X   |    X     |
| validFrom      | Date          | When the configuration becomes active                          |   X   |          |
| validTo        | Date          | When the configuration expires                                 |   X   |          |
| lastModifiedBy | ObjectId      | User who last modified the configuration                       |         |    X     |
| metadata       | Object        | Additional configuration metadata                              |         |          |
| createdAt      | Date          | Configuration creation timestamp                               |   X   |    X     |
| updatedAt      | Date          | Last update timestamp                                          |   X   |    X     |

## Metadata Sub-document Schema

```json
{
  "changeReason": String,
  "approvedBy": ObjectId,
  "approvalDate": Date,
  "dependencies": [String],
  "affectedServices": [String],
  "rollbackVersion": Number
}
```

## Indexing Strategy

* **Unique index:** `key`, `environment`, `version` (for unique configurations)
* **Compound index:** `category`, `isActive` (for active configurations by category)
* **Compound index:** `validFrom`, `validTo` (for time-based configurations)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "key": "job_posting.moderation.enabled",
  "value": true,
  "type": "boolean",
  "category": "moderation",
  "description": "Enable/disable job posting moderation",
  "environment": "production",
  "isActive": true,
  "version": 2,
  "validFrom": ISODate("2024-03-01T00:00:00Z"),
  "validTo": null,
  "lastModifiedBy": ObjectId("..."),
  "metadata": {
    "changeReason": "Enable mandatory moderation for all job postings",
    "approvedBy": ObjectId("..."),
    "approvalDate": ISODate("2024-02-28"),
    "dependencies": [
      "moderation.workflow.enabled",
      "notification.moderation.enabled"
    ],
    "affectedServices": [
      "job-posting-service",
      "moderation-service"
    ],
    "rollbackVersion": 1
  },
  "createdAt": ISODate("2024-03-01"),
  "updatedAt": ISODate("2024-03-01")
}
```