# Collection: integration_configs

## Description

Stores configuration settings and credentials for external system integrations.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the integration config                   |   X   |    X     |
| name           | String        | Integration name                                               |   X   |    X     |
| provider       | String        | Integration provider/vendor                                    |   X   |    X     |
| type           | String        | Integration type (oauth, api, etc)                            |   X   |    X     |
| status         | String        | Integration status (active, inactive)                         |   X   |    X     |
| credentials    | Object        | Encrypted credentials                                          |         |    X     |
| settings       | Object        | Integration-specific settings                                  |         |          |
| endpoints      | [Object]      | API endpoints configuration                                    |         |          |
| rateLimit      | Object        | Rate limiting settings                                        |         |          |
| retryConfig    | Object        | Retry configuration                                           |         |          |
| metadata       | Object        | Additional integration metadata                                |         |          |
| version        | String        | Integration version                                           |   X   |    X     |
| createdBy      | ObjectId      | Reference to user who created the config                      |   X   |    X     |
| updatedBy      | ObjectId      | Reference to user who last updated the config                 |   X   |    X     |
| createdAt      | Date          | Config creation timestamp                                     |   X   |    X     |
| updatedAt      | Date          | Last update timestamp                                         |   X   |    X     |

## Credentials Sub-document Schema

```json
{
  "clientId": String,
  "clientSecret": String,
  "apiKey": String,
  "accessToken": String,
  "refreshToken": String,
  "tokenExpiry": Date,
  "certificate": String,
  "privateKey": String
}
```

## Settings Sub-document Schema

```json
{
  "scope": [String],
  "redirectUri": String,
  "webhookUrl": String,
  "environment": String,
  "timeout": Number,
  "headers": Object,
  "parameters": Object
}
```

## Endpoints Sub-document Schema

```json
{
  "name": String,
  "url": String,
  "method": String,
  "headers": Object,
  "parameters": Object,
  "authentication": {
    "type": String,
    "value": String
  }
}
```

## Rate Limit Sub-document Schema

```json
{
  "requestsPerSecond": Number,
  "requestsPerMinute": Number,
  "requestsPerHour": Number,
  "concurrent": Number,
  "cooldownPeriod": Number
}
```

## Indexing Strategy

* **Compound index:** `provider`, `type` (for provider lookup)
* **Compound index:** `status`, `type` (for active integrations)
* **Index:** `version` (for version management)
* **Index:** `updatedAt` (for sync tracking)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "name": "LinkedIn Job Distribution",
  "provider": "linkedin",
  "type": "oauth2",
  "status": "active",
  "credentials": {
    "clientId": "encrypted_client_id",
    "clientSecret": "encrypted_client_secret",
    "accessToken": "encrypted_access_token",
    "refreshToken": "encrypted_refresh_token",
    "tokenExpiry": ISODate("2024-04-20T00:00:00Z")
  },
  "settings": {
    "scope": [
      "r_jobs",
      "w_jobs",
      "rw_organization"
    ],
    "redirectUri": "https://jobs.example.com/integrations/linkedin/callback",
    "webhookUrl": "https://jobs.example.com/webhooks/linkedin",
    "environment": "production",
    "timeout": 30000,
    "headers": {
      "X-Api-Version": "202403",
      "Accept-Language": "en-US"
    },
    "parameters": {
      "format": "json",
      "company_id": "12345"
    }
  },
  "endpoints": [
    {
      "name": "postJob",
      "url": "https://api.linkedin.com/v2/jobs",
      "method": "POST",
      "headers": {
        "Content-Type": "application/json"
      },
      "parameters": {
        "visibility": "public"
      },
      "authentication": {
        "type": "bearer",
        "value": "token"
      }
    },
    {
      "name": "getApplications",
      "url": "https://api.linkedin.com/v2/jobs/{job_id}/applications",
      "method": "GET",
      "authentication": {
        "type": "bearer",
        "value": "token"
      }
    }
  ],
  "rateLimit": {
    "requestsPerSecond": 10,
    "requestsPerMinute": 300,
    "requestsPerHour": 5000,
    "concurrent": 5,
    "cooldownPeriod": 60
  },
  "retryConfig": {
    "maxAttempts": 3,
    "initialDelay": 1000,
    "maxDelay": 5000,
    "backoffMultiplier": 2
  },
  "metadata": {
    "description": "LinkedIn integration for job posting and application tracking",
    "team": "Integrations",
    "documentation": "https://docs.example.com/integrations/linkedin",
    "supportContact": "<EMAIL>",
    "lastSync": ISODate("2024-03-20T15:30:00Z"),
    "healthStatus": {
      "status": "healthy",
      "lastCheck": ISODate("2024-03-20T15:30:00Z"),
      "errorRate": 0.001
    }
  },
  "version": "2.0.0",
  "createdBy": ObjectId("..."),
  "updatedBy": ObjectId("..."),
  "createdAt": ISODate("2024-01-01"),
  "updatedAt": ISODate("2024-03-20")
}