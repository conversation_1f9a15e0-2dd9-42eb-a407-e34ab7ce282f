# Collection: resume_analytics

## Description

Tracks and analyzes resume performance and visibility metrics.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for analytics                                |   X   |    X     |
| resumeId       | ObjectId      | Reference to the resume                                        |   X   |    X     |
| userId         | ObjectId      | Reference to the user                                          |   X   |    X     |
| views          | Number        | Total number of views                                          |   X   |          |
| uniqueViews    | Number        | Number of unique viewers                                       |   X   |          |
| viewsByEmployer| [Object]      | List of employers who viewed                                   |         |          |
| searchAppearances| Number      | Times appeared in search results                               |   X   |          |
| downloadCount  | Number        | Number of downloads                                            |         |          |
| applicationCount| Number       | Number of applications using this resume                       |   X   |          |
| interviewRate  | Number        | Percentage of applications leading to interviews               |   X   |          |
| skillMatches   | Object        | Matching statistics for skills                                 |         |          |
| viewsByDate    | Object        | Daily view counts                                              |         |          |
| lastViewed     | Date          | Last view timestamp                                            |   X   |          |
| createdAt      | Date          | Analytics creation timestamp                                   |   X   |    X     |
| updatedAt      | Date          | Last update timestamp                                          |   X   |    X     |

## Employer View Sub-document Schema

```json
{
  "employerId": ObjectId,
  "companyName": String,
  "viewCount": Number,
  "lastViewed": Date,
  "jobPostingId": ObjectId,
  "actionTaken": String
}
```

## Skill Matches Schema

```json
{
  "totalJobsAnalyzed": Number,
  "matchedSkills": {
    "skillName": {
      "matchCount": Number,
      "relevanceScore": Number
    }
  }
}
```

## Indexing Strategy

* **Compound index:** `resumeId`, `createdAt` (for resume performance tracking)
* **Compound index:** `userId`, `views` (for user's most viewed resumes)
* **Compound index:** `interviewRate`, `applicationCount` (for success metrics)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "resumeId": ObjectId("..."),
  "userId": ObjectId("..."),
  "views": 125,
  "uniqueViews": 85,
  "viewsByEmployer": [
    {
      "employerId": ObjectId("..."),
      "companyName": "Tech Corp",
      "viewCount": 3,
      "lastViewed": ISODate("2024-03-20"),
      "jobPostingId": ObjectId("..."),
      "actionTaken": "invited_to_interview"
    }
  ],
  "searchAppearances": 450,
  "downloadCount": 12,
  "applicationCount": 15,
  "interviewRate": 33.3,
  "skillMatches": {
    "totalJobsAnalyzed": 200,
    "matchedSkills": {
      "React": {
        "matchCount": 180,
        "relevanceScore": 0.9
      },
      "Node.js": {
        "matchCount": 150,
        "relevanceScore": 0.75
      }
    }
  },
  "viewsByDate": {
    "2024-03-20": 15,
    "2024-03-19": 12
  },
  "lastViewed": ISODate("2024-03-20T15:30:00Z"),
  "createdAt": ISODate("2024-03-01T00:00:00Z"),
  "updatedAt": ISODate("2024-03-20T15:30:00Z")
}
``` 