# Collection: search_indexes

## Description

Manages search index configurations, mappings, and settings for various searchable content types.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the search index                         |   X   |    X     |
| name           | String        | Index name                                                     |   X   |    X     |
| type           | String        | Content type (jobs, profiles, companies, etc)                  |   X   |    X     |
| status         | String        | Index status (active, building, error)                         |   X   |    X     |
| version        | String        | Index version                                                  |   X   |    X     |
| mappings       | Object        | Field mappings and analyzers                                   |         |    X     |
| settings       | Object        | Index settings and configuration                               |         |    X     |
| aliases        | [String]      | Index aliases                                                  |   X   |          |
| stats          | Object        | Index statistics                                               |         |          |
| maintenance    | Object        | Maintenance schedule and status                                |         |          |
| metadata       | Object        | Additional index metadata                                      |         |          |
| createdBy      | ObjectId      | Reference to user who created the index                       |   X   |    X     |
| updatedBy      | ObjectId      | Reference to user who last updated the index                  |   X   |    X     |
| createdAt      | Date          | Index creation timestamp                                       |   X   |    X     |
| updatedAt      | Date          | Last update timestamp                                          |   X   |    X     |

## Mappings Sub-document Schema

```json
{
  "properties": {
    "field_name": {
      "type": String,
      "analyzer": String,
      "boost": Number,
      "fields": Object,
      "copyTo": [String]
    }
  },
  "dynamic_templates": [Object],
  "analyzers": {
    "analyzer_name": {
      "type": String,
      "tokenizer": String,
      "filters": [String]
    }
  }
}
```

## Settings Sub-document Schema

```json
{
  "number_of_shards": Number,
  "number_of_replicas": Number,
  "refresh_interval": String,
  "max_result_window": Number,
  "analysis": {
    "filter": Object,
    "char_filter": Object,
    "tokenizer": Object
  },
  "similarity": Object
}
```

## Stats Sub-document Schema

```json
{
  "documentCount": Number,
  "storageSize": Number,
  "lastIndexed": Date,
  "indexingRate": Number,
  "queryRate": Number,
  "avgResponseTime": Number
}
```

## Indexing Strategy

* **Compound index:** `type`, `status` (for active indexes)
* **Compound index:** `name`, `version` (for version management)
* **Index:** `aliases` (for alias lookup)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "name": "jobs_v2",
  "type": "jobs",
  "status": "active",
  "version": "2.0.0",
  "mappings": {
    "properties": {
      "title": {
        "type": "text",
        "analyzer": "job_title_analyzer",
        "boost": 2.0,
        "fields": {
          "keyword": {
            "type": "keyword"
          },
          "suggest": {
            "type": "completion"
          }
        }
      },
      "description": {
        "type": "text",
        "analyzer": "job_content_analyzer"
      },
      "skills": {
        "type": "keyword",
        "normalizer": "skill_normalizer"
      },
      "location": {
        "type": "geo_point"
      },
      "salary": {
        "type": "nested",
        "properties": {
          "min": { "type": "float" },
          "max": { "type": "float" },
          "currency": { "type": "keyword" }
        }
      }
    },
    "dynamic_templates": [
      {
        "strings_as_keywords": {
          "match_mapping_type": "string",
          "mapping": {
            "type": "keyword"
          }
        }
      }
    ],
    "analyzers": {
      "job_title_analyzer": {
        "type": "custom",
        "tokenizer": "standard",
        "filters": [
          "lowercase",
          "stop",
          "job_synonyms"
        ]
      }
    }
  },
  "settings": {
    "number_of_shards": 3,
    "number_of_replicas": 1,
    "refresh_interval": "1s",
    "max_result_window": 10000,
    "analysis": {
      "filter": {
        "job_synonyms": {
          "type": "synonym",
          "synonyms": [
            "developer, engineer, programmer",
            "frontend, front-end, front end"
          ]
        }
      }
    }
  },
  "aliases": [
    "jobs_current",
    "jobs_search"
  ],
  "stats": {
    "documentCount": 150000,
    "storageSize": 500000000,
    "lastIndexed": ISODate("2024-03-20T15:00:00Z"),
    "indexingRate": 100,
    "queryRate": 1000,
    "avgResponseTime": 50
  },
  "maintenance": {
    "schedule": {
      "reindex": "0 0 * * 0",
      "optimize": "0 0 * * *"
    },
    "lastReindex": ISODate("2024-03-15"),
    "lastOptimize": ISODate("2024-03-20"),
    "status": {
      "health": "green",
      "reindexing": false,
      "optimizing": false
    }
  },
  "metadata": {
    "description": "Enhanced job search index with geo-location support",
    "owner": "Search Team",
    "environment": "production",
    "dependencies": [
      "job_postings",
      "companies",
      "locations"
    ],
    "changelog": [
      {
        "version": "2.0.0",
        "changes": [
          "Added geo-location support",
          "Enhanced skill normalization"
        ],
        "date": ISODate("2024-03-01")
      }
    ]
  },
  "createdBy": ObjectId("..."),
  "updatedBy": ObjectId("..."),
  "createdAt": ISODate("2024-03-01"),
  "updatedAt": ISODate("2024-03-20")
}