# Collection: skills

## Description

Stores skills and proficiency levels for job seekers and job requirements.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the skill entry                          |   X   |    X     |
| resumeId       | ObjectId      | Reference to the resume (if associated)                        |   X   |          |
| name           | String        | Name of the skill                                              |   X   |    X     |
| category       | String        | Skill category (technical, soft, language, etc.)               |   X   |    X     |
| proficiency    | String        | Proficiency level (Beginner, Intermediate, Expert)             |   X   |          |
| yearsOfExperience | Number     | Years of experience with the skill                             |   X   |          |
| lastUsed       | Date          | When the skill was last used                                   |         |          |
| endorsements   | Number        | Number of endorsements received                                |         |          |
| isVerified     | Boolean       | Whether the skill has been verified                            |         |          |
| projects       | [String]      | Projects where skill was used                                  |         |          |
| createdAt      | Date          | Entry creation timestamp                                       |         |    X     |
| updatedAt      | Date          | Last entry update timestamp                                    |         |    X     |

## Indexing Strategy

* **Text index:** `name` (for skill search)
* **Compound index:** `resumeId`, `proficiency` (for resume skill queries)
* **Compound index:** `category`, `name` (for category-based queries)
* **Compound index:** `name`, `endorsements` (for skill popularity)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "resumeId": ObjectId("..."),
  "name": "React.js",
  "category": "technical",
  "proficiency": "Expert",
  "yearsOfExperience": 4,
  "lastUsed": ISODate("2024-03-20"),
  "endorsements": 12,
  "isVerified": true,
  "projects": [
    "E-commerce Platform",
    "Social Media Dashboard"
  ],
  "createdAt": ISODate("2024-03-20"),
  "updatedAt": ISODate("2024-03-20")
}
``` 