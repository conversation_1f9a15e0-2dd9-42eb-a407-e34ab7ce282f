# Collection: screening_questions

## Description

Stores pre-screening questions for job applications.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the question                             |   X   |    X     |
| jobPostingId   | ObjectId      | Reference to the job posting                                   |   X   |    X     |
| questionText   | String        | The text of the question                                       |         |    X     |
| questionType   | String        | Type (multiple_choice, text, yes_no)                           |   X   |    X     |
| options        | [String]      | Options for multiple-choice questions                          |         |          |
| correctAnswer  | String        | Expected/correct answer (if applicable)                        |         |          |
| required       | Boolean       | Whether the question is mandatory                              |         |    X     |
| weight         | Number        | Weight for scoring responses                                   |         |          |
| order          | Number        | Display order of the question                                  |   X   |    X     |
| maxLength      | Number        | Maximum length for text answers                                |         |          |
| hint           | String        | Hint text for answering the question                           |         |          |
| createdAt      | Date          | Question creation timestamp                                    |         |    X     |
| updatedAt      | Date          | Last question update timestamp                                 |         |    X     |

## Indexing Strategy

* **Compound index:** `jobPostingId`, `order` (for retrieving questions in order)
* **Compound index:** `jobPostingId`, `required` (for filtering required questions)
* **Text index:** `questionText` (for searching questions)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "jobPostingId": ObjectId("..."),
  "questionText": "How many years of experience do you have with cloud technologies?",
  "questionType": "multiple_choice",
  "options": [
    "No experience",
    "1-2 years",
    "3-5 years",
    "5+ years"
  ],
  "correctAnswer": "3-5 years",
  "required": true,
  "weight": 2,
  "order": 1,
  "hint": "Include both professional and personal project experience",
  "createdAt": ISODate("2024-03-20"),
  "updatedAt": ISODate("2024-03-20")
}
``` 