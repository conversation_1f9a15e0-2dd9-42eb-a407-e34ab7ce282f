# Collection: job_posting_analytics

## Description

Tracks and analyzes performance metrics for job postings.

## Schema

| Field              | Type          | Description                                                    | Indexed | Required |
| ----------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id               | ObjectId      | Unique identifier for analytics                                |   X   |    X     |
| jobPostingId      | ObjectId      | Reference to job posting                                       |   X   |    X     |
| employerId        | ObjectId      | Reference to employer                                          |   X   |    X     |
| views             | Number        | Total number of views                                          |   X   |          |
| uniqueViews       | Number        | Number of unique visitors                                      |   X   |          |
| viewsByDate       | Object        | Daily view counts                                              |         |          |
| averageViewDuration| Number       | Average time spent viewing (seconds)                           |         |          |
| clickThroughRate  | Number        | Click-through rate percentage                                  |   X   |          |
| applications      | Number        | Total number of applications                                   |   X   |          |
| applicationsByDate| Object        | Daily application counts                                       |         |          |
| conversionRate    | Number        | Views to applications ratio                                    |   X   |          |
| applicationQuality| Object        | Application quality metrics                                    |         |          |
| demographics      | Object        | Viewer/applicant demographics                                  |         |          |
| sourceStats       | Object        | Traffic sources statistics                                     |         |          |
| searchMatches     | Number        | Times appeared in search results                               |   X   |          |
| savedCount        | Number        | Number of times saved by users                                 |         |          |
| startDate         | Date          | Analytics tracking start date                                  |   X   |    X     |
| endDate           | Date          | Analytics tracking end date                                    |   X   |          |
| createdAt         | Date          | Analytics creation timestamp                                   |   X   |    X     |
| updatedAt         | Date          | Last update timestamp                                          |   X   |    X     |

## Demographics Sub-document Schema

```json
{
  "locations": {
    "locationName": Number
  },
  "experienceLevels": {
    "levelName": Number
  },
  "skills": {
    "skillName": Number
  }
}
```

## Application Quality Schema

```json
{
  "averageScore": Number,
  "qualifiedCandidates": Number,
  "shortlistedCount": Number,
  "interviewedCount": Number,
  "hiredCount": Number,
  "skillMatch": {
    "high": Number,
    "medium": Number,
    "low": Number
  }
}
```

## Indexing Strategy

* **Compound index:** `jobPostingId`, `startDate` (for tracking period)
* **Compound index:** `employerId`, `conversionRate` (for employer analytics)
* **Compound index:** `applications`, `views` (for performance metrics)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "jobPostingId": ObjectId("..."),
  "employerId": ObjectId("..."),
  "views": 1250,
  "uniqueViews": 980,
  "viewsByDate": {
    "2024-03-20": 150,
    "2024-03-19": 125
  },
  "averageViewDuration": 145,
  "clickThroughRate": 8.5,
  "applications": 45,
  "applicationsByDate": {
    "2024-03-20": 8,
    "2024-03-19": 6
  },
  "conversionRate": 3.6,
  "applicationQuality": {
    "averageScore": 7.8,
    "qualifiedCandidates": 30,
    "shortlistedCount": 15,
    "interviewedCount": 8,
    "hiredCount": 2,
    "skillMatch": {
      "high": 20,
      "medium": 15,
      "low": 10
    }
  },
  "demographics": {
    "locations": {
      "San Francisco": 450,
      "New York": 280
    },
    "experienceLevels": {
      "senior": 600,
      "mid-level": 400
    }
  },
  "sourceStats": {
    "direct": 400,
    "search": 600,
    "referral": 250
  },
  "searchMatches": 850,
  "savedCount": 75,
  "startDate": ISODate("2024-03-01"),
  "endDate": ISODate("2024-03-20"),
  "createdAt": ISODate("2024-03-01"),
  "updatedAt": ISODate("2024-03-20")
}
``` 