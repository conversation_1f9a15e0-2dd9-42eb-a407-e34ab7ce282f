# Collection: job_alerts

## Description

Manages automated job alerts based on user search criteria.

## Schema

| Field              | Type          | Description                                                    | Indexed | Required |
| ----------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id               | ObjectId      | Unique identifier for the job alert                            |   X   |    X     |
| userId            | ObjectId      | Reference to User                                              |   X   |    X     |
| name              | String        | Alert name                                                     |         |    X     |
| keywords          | [String]      | Search keywords                                                |   X   |          |
| locations         | [String]      | Location preferences                                           |   X   |          |
| jobTypes          | [String]      | Job types                                                      |   X   |          |
| industries        | [String]      | Industry preferences                                           |   X   |          |
| salaryRange       | Object        | {min: Number, max: Number, currency: String}                   |         |          |
| experienceLevels  | [String]      | Experience levels                                              |   X   |          |
| remotePreference  | String        | Remote work preference                                         |   X   |          |
| companyTypes      | [String]      | Company type preferences                                       |   X   |          |
| frequency         | String        | Alert frequency (daily, weekly)                                |   X   |    X     |
| emailNotification | Boolean       | Email notification preference                                  |         |    X     |
| pushNotification  | Boolean       | Push notification preference                                   |         |    X     |
| lastSentAt        | Date          | Last notification timestamp                                    |   X   |          |
| matchCount        | Number        | Number of matches found                                        |         |          |
| status            | String        | Alert status (active, paused)                                  |   X   |    X     |
| createdAt         | Date          | Alert creation timestamp                                       |   X   |    X     |
| updatedAt         | Date          | Last alert update timestamp                                    |   X   |    X     |

## Indexing Strategy

* **Compound index:** `userId`, `status` (for user's active alerts)
* **Compound index:** `lastSentAt`, `frequency` (for notification processing)
* **Text index:** `keywords` (for matching with job postings)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "userId": ObjectId("..."),
  "name": "Senior Developer Positions",
  "keywords": ["senior", "software engineer", "full stack"],
  "locations": ["San Francisco", "Remote"],
  "jobTypes": ["full-time"],
  "industries": ["Technology", "Finance"],
  "salaryRange": {
    "min": 150000,
    "max": 250000,
    "currency": "USD"
  },
  "experienceLevels": ["senior"],
  "remotePreference": "hybrid",
  "companyTypes": ["startup", "enterprise"],
  "frequency": "daily",
  "emailNotification": true,
  "pushNotification": true,
  "lastSentAt": ISODate("2024-03-20T00:00:00Z"),
  "matchCount": 15,
  "status": "active",
  "createdAt": ISODate("2024-03-20"),
  "updatedAt": ISODate("2024-03-20")
}
``` 