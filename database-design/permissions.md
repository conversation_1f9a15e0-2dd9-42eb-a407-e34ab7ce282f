# Collection: permissions

## Description

Defines granular permissions that can be assigned to roles.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the permission                           |   X   |    X     |
| code           | String        | Unique permission code                                         |   X   |    X     |
| name           | String        | Human-readable permission name                                 |   X   |    X     |
| description    | String        | Detailed permission description                                |         |    X     |
| category       | String        | Permission category                                            |   X   |    X     |
| resource       | String        | Resource being accessed                                        |   X   |    X     |
| action         | String        | Allowed action (create, read, update, delete)                  |   X   |    X     |
| scope          | String        | Permission scope                                               |   X   |          |
| isSystem       | Boolean       | Whether it's a system permission                               |   X   |    X     |
| dependencies   | [String]      | Other permissions required                                     |         |          |
| conditions     | Object        | Conditional restrictions                                       |         |          |
| metadata       | Object        | Additional permission metadata                                 |         |          |
| createdAt      | Date          | Permission creation timestamp                                  |   X   |    X     |
| updatedAt      | Date          | Last update timestamp                                          |   X   |    X     |

## Conditions Sub-document Schema

```json
{
  "timeRestrictions": {
    "days": [String],
    "hours": [Number]
  },
  "locationRestrictions": [String],
  "ipRestrictions": [String],
  "roleRestrictions": [String],
  "customRules": Object
}
```

## Indexing Strategy

* **Unique index:** `code` (for unique permission codes)
* **Compound index:** `category`, `resource` (for permission grouping)
* **Compound index:** `isSystem`, `action` (for system permissions)
* **Text index:** `name`, `description` (for permission search)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "code": "MANAGE_JOB_POSTINGS",
  "name": "Manage Job Postings",
  "description": "Create, edit, and delete job postings",
  "category": "jobs",
  "resource": "job_posting",
  "action": "manage",
  "scope": "organization",
  "isSystem": true,
  "dependencies": [
    "VIEW_JOB_POSTINGS",
    "VIEW_APPLICATIONS"
  ],
  "conditions": {
    "timeRestrictions": {
      "days": ["WEEKDAY"],
      "hours": [9, 10, 11, 12, 13, 14, 15, 16, 17]
    },
    "locationRestrictions": ["office", "vpn"],
    "ipRestrictions": ["10.0.0.0/8"],
    "roleRestrictions": ["hr", "recruiter"],
    "customRules": {
      "requiresApproval": true,
      "maxPostingsPerDay": 50
    }
  },
  "metadata": {
    "icon": "file-text",
    "uiSection": "job-management",
    "auditLevel": "high",
    "riskLevel": "medium"
  },
  "createdAt": ISODate("2024-03-01"),
  "updatedAt": ISODate("2024-03-20")
}
``` 