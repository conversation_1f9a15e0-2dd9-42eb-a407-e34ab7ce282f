# Collection: users

## Description

Stores information about all users: JobSeekers, Employers, and Admins. Optimized for frequent access and authentication.

## Schema

| Field          | Type      | Description                                                                  | Indexed | Required | Unique |
| -------------- | --------- | ---------------------------------------------------------------------------- | :-----: | :------: | :----: |
| \_id          | ObjectId  | Unique identifier for the user.                                              |   X   |    X     |   X  |
| username       | String    | Username for login.                                                          |   X   |    X     |   X  |
| email          | String    | Email address (used for communication and password recovery).                |   X   |    X     |   X  |
| password       | String    | Hashed password for authentication.                                          |         |    X     |      |
| createdAt      | Date      | Account creation timestamp.                                                   |   X   |    X     |      |
| updatedAt      | Date      | Last profile/account update timestamp.                                       |   X   |    X     |      |
| status         | String    | Status ("active", "suspended", "unverified").                               |   X   |    X     |      |
| role           | String    | User role ("jobseeker", "employer", "admin").                                |   X   |    X     |      |
| profile        | Object    | Embedded document with role-specific details (JobSeeker, Employer, or Admin). |         |    X     |      |
| lastLogin      | Date      | Timestamp of the user's last login.                                          |   X   |          |      |

## Embedded Document: JobSeeker (profile)

| Field         | Type      | Description                                                     | Indexed | Required |
| -------------- | --------- | --------------------------------------------------------------- | :-----: | :------: |
| firstName     | String    | First name.                                                     |   X     |    X     |
| lastName      | String    | Last name.                                                      |   X     |    X     |
| mobile        | String    | Mobile number.                                                  |         |          |
| resumeId      | ObjectId  | Reference to the user's primary resume in the `resumes` collection. |         |          |
| skills        | Array     | Array of strings representing the job seeker's skills.          |   X     |          |
| experience    | Array     | Array of experience objects (see Experience schema).             |         |          |
| education     | Array     | Array of education objects (see Education schema).               |         |          |
| location      | String    | Job seeker's current location.                                   |         |          |

## Embedded Document: Employer (profile)

| Field          | Type      | Description                                                         | Indexed | Required |
| -------------- | --------- | ------------------------------------------------------------------- | :-----: | :------: |
| companyId      | ObjectId  | Reference to the company profile in the `companies` collection.    |   X     |    X     |
| mobile        | String    | Contact mobile number for the employer user.                        |         |          |
| designation   | String    | Job title/designation of the employer user within the company.      |         |          |
| teamMembers   | Array     | Array of ObjectIds of other `users` (role: "employer") who are part of the same company. |         |          |

## Embedded Document: Admin (profile)

| Field | Type | Description | Indexed | Required |
| ----- | ---- | ----------- | :-----: | :------: |
|       |      |             |         |          |

## Embedded Document: Experience

| Field        | Type   | Description                               |
| ------------ | ------ | ----------------------------------------- |
| companyName  | String | Name of the company.                      |
| title        | String | Job title.                                |
| startDate    | Date   | Start date of employment.                |
| endDate      | Date   | End date of employment (or null if present). |
| description  | String | Description of responsibilities.          |
| location     | String | Location of the job.                      |

## Embedded Document: Education

| Field         | Type   | Description                         |
| ------------- | ------ | ----------------------------------- |
| institution   | String | Name of the institution.            |
| degree        | String | Degree earned.                      |
| fieldOfStudy  | String | Major or field of study.            |
| startDate     | Date   | Start date of the degree program. |
| graduationDate| Date   | Graduation date (or expected date). |
| grade         | String | GPA or other grade information.      |

## Indexing Strategy

*   **Compound index:** `role`, `status`, `createdAt` (for efficiently querying users based on these common filters).
*   **Text index:** `profile.firstName`, `profile.lastName`, `profile.skills` (for jobseeker searches).
*   **Sparse index:** `profile.resumeId` (only index users who have a resume).