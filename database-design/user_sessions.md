# Collection: user_sessions

## Description

Tracks user authentication sessions and activity state.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the session                              |   X   |    X     |
| userId         | ObjectId      | Reference to user                                              |   X   |    X     |
| token          | String        | Session token                                                  |   X   |    X     |
| refreshToken   | String        | Refresh token                                                  |   X   |          |
| deviceId       | String        | Unique device identifier                                       |   X   |          |
| platform       | String        | Platform (web, mobile, api)                                    |   X   |    X     |
| userAgent      | String        | Browser/client user agent                                      |         |          |
| ipAddress      | String        | IP address                                                     |   X   |    X     |
| location       | Object        | Geographic location info                                        |         |          |
| isActive       | Boolean       | Whether session is active                                      |   X   |    X     |
| lastActivity   | Date          | Last activity timestamp                                        |   X   |    X     |
| expiresAt      | Date          | Session expiration timestamp                                   |   X   |    X     |
| metadata       | Object        | Additional session metadata                                     |         |          |
| createdAt      | Date          | Session creation timestamp                                     |   X   |    X     |
| updatedAt      | Date          | Last update timestamp                                          |   X   |    X     |

## Location Sub-document Schema

```json
{
  "country": String,
  "city": String,
  "coordinates": {
    "latitude": Number,
    "longitude": Number
  },
  "timezone": String
}
```

## Metadata Sub-document Schema

```json
{
  "deviceInfo": {
    "type": String,
    "os": String,
    "browser": String,
    "version": String
  },
  "securityInfo": {
    "mfaVerified": Boolean,
    "loginMethod": String,
    "riskScore": Number
  },
  "permissions": [String],
  "features": [String]
}
```

## Indexing Strategy

* **Compound index:** `userId`, `isActive` (for active user sessions)
* **Compound index:** `token`, `expiresAt` (for session validation)
* **TTL index:** `expiresAt` (for automatic session cleanup)
* **Compound index:** `deviceId`, `userId` (for device-specific sessions)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "userId": ObjectId("..."),
  "token": "eyJhbGciOiJIUzI1NiIs...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
  "deviceId": "device_789xyz",
  "platform": "web",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) ...",
  "ipAddress": "***********",
  "location": {
    "country": "US",
    "city": "San Francisco",
    "coordinates": {
      "latitude": 37.7749,
      "longitude": -122.4194
    },
    "timezone": "America/Los_Angeles"
  },
  "isActive": true,
  "lastActivity": ISODate("2024-03-20T15:30:00Z"),
  "expiresAt": ISODate("2024-03-21T15:30:00Z"),
  "metadata": {
    "deviceInfo": {
      "type": "desktop",
      "os": "Windows",
      "browser": "Chrome",
      "version": "122.0.0"
    },
    "securityInfo": {
      "mfaVerified": true,
      "loginMethod": "password",
      "riskScore": 0.1
    },
    "permissions": [
      "VIEW_JOBS",
      "APPLY_JOBS"
    ],
    "features": [
      "beta_search",
      "new_dashboard"
    ]
  },
  "createdAt": ISODate("2024-03-20T10:30:00Z"),
  "updatedAt": ISODate("2024-03-20T15:30:00Z")
}
``` 