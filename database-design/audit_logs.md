# Collection: audit_logs

## Description

Tracks system activities and changes for audit purposes. Write-heavy collection optimized for logging.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the audit log entry                      |   X   |    X     |
| timestamp      | Date          | When the action occurred                                       |   X   |    X     |
| userId         | ObjectId      | User who performed the action                                  |   X   |    X     |
| ipAddress      | String        | IP address of request                                          |         |    X     |
| userAgent      | String        | Browser/client information                                     |         |          |
| action         | String        | Type of action performed                                       |   X   |    X     |
| entityType     | String        | Type of entity affected                                        |   X   |    X     |
| entityId       | ObjectId      | ID of affected entity                                          |   X   |    X     |
| status         | String        | Outcome status                                                 |   X   |          |
| description    | String        | Detailed description                                           |         |    X     |
| previousState  | Object        | State before change                                            |         |          |
| newState       | Object        | State after change                                             |         |          |
| changes        | [String]      | List of changed fields                                         |         |          |
| reason         | String        | Reason for change                                              |         |          |
| metadata       | Object        | Additional contextual data                                     |         |          |

## Indexing Strategy

* **Compound index:** `timestamp`, `entityType`, `entityId` (for entity history queries)
* **Compound index:** `userId`, `timestamp` (for user activity history)
* **Compound index:** `action`, `status`, `timestamp` (for filtering specific actions)
* **TTL index:** Optional TTL index on `timestamp` for log rotation if needed

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "timestamp": ISODate("2024-03-20T10:30:00Z"),
  "userId": ObjectId("..."),
  "ipAddress": "***********",
  "userAgent": "Mozilla/5.0...",
  "action": "UPDATE_JOB_POSTING",
  "entityType": "job_posting",
  "entityId": ObjectId("..."),
  "status": "success",
  "description": "Updated job posting salary range",
  "previousState": {
    "salary": {
      "min": 80000,
      "max": 100000
    }
  },
  "newState": {
    "salary": {
      "min": 90000,
      "max": 120000
    }
  },
  "changes": ["salary"],
  "reason": "Market adjustment",
  "metadata": {
    "approver": ObjectId("..."),
    "source": "admin_panel"
  }
}
```