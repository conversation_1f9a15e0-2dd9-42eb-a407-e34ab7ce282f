# Collection: system_events

## Description

Manages system-wide events for broadcasting and handling across services.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the event                                |   X   |    X     |
| name           | String        | Event name                                                     |   X   |    X     |
| type           | String        | Event type (system, application, integration)                  |   X   |    X     |
| source         | String        | Event source service/component                                 |   X   |    X     |
| severity       | String        | Event severity (info, warning, error, critical)                |   X   |    X     |
| status         | String        | Event status (pending, processing, completed)                  |   X   |    X     |
| data           | Object        | Event payload data                                             |         |    X     |
| metadata       | Object        | Additional event metadata                                      |         |          |
| handlers       | [Object]      | Event handler configurations                                   |         |          |
| correlationId  | String        | Correlation ID for event tracking                              |   X   |          |
| version        | String        | Event schema version                                           |   X   |    X     |
| broadcast      | Object        | Broadcasting configuration                                     |         |          |
| acknowledgments| [Object]      | Handler acknowledgments                                        |         |          |
| retries        | Object        | Retry configuration and state                                  |         |          |
| expiresAt      | Date          | Event expiration timestamp                                     |   X   |          |
| createdAt      | Date          | Event creation timestamp                                       |   X   |    X     |
| updatedAt      | Date          | Last update timestamp                                          |   X   |    X     |

## Data Sub-document Schema

```json
{
  "payload": Mixed,
  "schema": String,
  "version": String,
  "encoding": String,
  "compressed": Boolean
}
```

## Metadata Sub-document Schema

```json
{
  "environment": String,
  "region": String,
  "trace": {
    "id": String,
    "span": String
  },
  "user": {
    "id": ObjectId,
    "type": String
  },
  "tags": [String]
}
```

## Handlers Sub-document Schema

```json
{
  "service": String,
  "endpoint": String,
  "method": String,
  "timeout": Number,
  "retries": Number,
  "filters": [Object],
  "transform": Object
}
```

## Broadcast Sub-document Schema

```json
{
  "channels": [String],
  "priority": Number,
  "delay": Number,
  "expiry": Number,
  "targets": [String]
}
```

## Indexing Strategy

* **Compound index:** `type`, `status` (for event processing)
* **Compound index:** `source`, `createdAt` (for source tracking)
* **Compound index:** `correlationId`, `name` (for event correlation)
* **TTL index:** `expiresAt` (for event cleanup)

## Sample Documents

```json
{
  "_id": ObjectId("..."),
  "name": "job.status_changed",
  "type": "application",
  "source": "job_service",
  "severity": "info",
  "status": "pending",
  "data": {
    "payload": {
      "jobId": ObjectId("..."),
      "oldStatus": "draft",
      "newStatus": "published",
      "changes": {
        "visibility": "public",
        "publishedAt": ISODate("2024-03-20T15:30:00Z")
      }
    },
    "schema": "job_status_change/v1",
    "version": "1.0.0",
    "encoding": "json",
    "compressed": false
  },
  "metadata": {
    "environment": "production",
    "region": "us-east-1",
    "trace": {
      "id": "trace_abc123",
      "span": "span_xyz789"
    },
    "user": {
      "id": ObjectId("..."),
      "type": "recruiter"
    },
    "tags": [
      "job_lifecycle",
      "status_change"
    ]
  },
  "handlers": [
    {
      "service": "notification_service",
      "endpoint": "/api/v1/notifications",
      "method": "POST",
      "timeout": 5000,
      "retries": 3,
      "filters": [
        {
          "field": "newStatus",
          "operator": "equals",
          "value": "published"
        }
      ],
      "transform": {
        "template": "job_status_notification",
        "version": "1.0"
      }
    },
    {
      "service": "search_service",
      "endpoint": "/api/v1/index",
      "method": "PUT",
      "timeout": 10000,
      "retries": 5,
      "filters": [],
      "transform": {
        "template": "job_index_update",
        "version": "1.0"
      }
    }
  ],
  "correlationId": "corr_def456",
  "version": "1.0.0",
  "broadcast": {
    "channels": [
      "job_updates",
      "search_updates"
    ],
    "priority": 1,
    "delay": 0,
    "expiry": 3600,
    "targets": [
      "notification_service",
      "search_service",
      "analytics_service"
    ]
  },
  "acknowledgments": [
    {
      "handler": "notification_service",
      "status": "success",
      "timestamp": ISODate("2024-03-20T15:30:01Z"),
      "metadata": {
        "notificationId": "not_789"
      }
    }
  ],
  "retries": {
    "count": 0,
    "maxAttempts": 3,
    "lastAttempt": null,
    "nextAttempt": null,
    "backoff": {
      "type": "exponential",
      "baseDelay": 1000
    }
  },
  "expiresAt": ISODate("2024-03-21T15:30:00Z"),
  "createdAt": ISODate("2024-03-20T15:30:00Z"),
  "updatedAt": ISODate("2024-03-20T15:30:01Z")
}