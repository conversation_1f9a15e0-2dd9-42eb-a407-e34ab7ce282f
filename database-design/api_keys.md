# Collection: api_keys

## Description

Manages API keys for external integrations and third-party access.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the API key                              |   X   |    X     |
| userId         | ObjectId      | Reference to user who owns the key                             |   X   |    X     |
| name           | String        | Key name/description                                           |   X   |    X     |
| key            | String        | Hashed API key                                                 |   X   |    X     |
| prefix         | String        | First few characters of the key (for display)                  |   X   |    X     |
| scopes         | [String]      | Allowed API scopes                                             |   X   |    X     |
| status         | String        | Key status (active, revoked, expired)                          |   X   |    X     |
| type           | String        | Key type (test, production)                                    |   X   |    X     |
| rateLimit      | Object        | Rate limiting configuration                                     |         |          |
| ipWhitelist    | [String]      | Allowed IP addresses                                           |         |          |
| lastUsed       | Date          | Last usage timestamp                                           |   X   |          |
| expiresAt      | Date          | Key expiration timestamp                                       |   X   |          |
| metadata       | Object        | Additional key metadata                                         |         |          |
| createdAt      | Date          | Key creation timestamp                                         |   X   |    X     |
| updatedAt      | Date          | Last update timestamp                                          |   X   |    X     |

## Rate Limit Sub-document Schema

```json
{
  "requestsPerMinute": Number,
  "requestsPerHour": Number,
  "requestsPerDay": Number,
  "concurrent": Number
}
```

## Metadata Sub-document Schema

```json
{
  "application": String,
  "environment": String,
  "contactEmail": String,
  "usageStats": {
    "totalRequests": Number,
    "lastRequestPath": String,
    "averageResponseTime": Number
  },
  "securityAudit": {
    "lastScan": Date,
    "riskLevel": String,
    "findings": [String]
  }
}
```

## Indexing Strategy

* **Unique index:** `key` (for key lookup)
* **Compound index:** `userId`, `status` (for user's active keys)
* **Compound index:** `type`, `expiresAt` (for key management)
* **TTL index:** `expiresAt` (for automatic key expiration)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "userId": ObjectId("..."),
  "name": "Job Board Integration",
  "key": "$2b$10$X7ZKj9k2R8B...",
  "prefix": "jb_prod_",
  "scopes": [
    "jobs:read",
    "jobs:write",
    "applications:read"
  ],
  "status": "active",
  "type": "production",
  "rateLimit": {
    "requestsPerMinute": 60,
    "requestsPerHour": 1000,
    "requestsPerDay": 10000,
    "concurrent": 5
  },
  "ipWhitelist": [
    "***********/24",
    "************"
  ],
  "lastUsed": ISODate("2024-03-20T15:30:00Z"),
  "expiresAt": ISODate("2025-03-20"),
  "metadata": {
    "application": "External Job Board",
    "environment": "production",
    "contactEmail": "<EMAIL>",
    "usageStats": {
      "totalRequests": 15000,
      "lastRequestPath": "/api/v1/jobs",
      "averageResponseTime": 250
    },
    "securityAudit": {
      "lastScan": ISODate("2024-03-15"),
      "riskLevel": "low",
      "findings": []
    }
  },
  "createdAt": ISODate("2024-03-01"),
  "updatedAt": ISODate("2024-03-20")
}
``` 