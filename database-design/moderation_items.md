# Collection: moderation_items

## Description

Manages content moderation workflow for various types of content.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the moderation item                      |   X   |    X     |
| contentType    | String        | Type of content (JobPosting, Resume, etc.)                     |   X   |    X     |
| contentId      | ObjectId      | ID of the content being moderated                              |   X   |    X     |
| moderatorId    | ObjectId      | ID of assigned moderator                                       |   X   |          |
| status         | String        | Moderation status (Pending, Approved, Rejected)                |   X   |    X     |
| priority       | String        | Moderation priority (High, Medium, Low)                        |   X   |    X     |
| reason         | String        | Reason for moderation                                          |         |          |
| notes          | String        | Moderator's notes                                              |         |          |
| decision       | String        | Final decision                                                 |         |          |
| decisionReason | String        | Reason for decision                                            |         |          |
| flags          | [String]      | Content flags (inappropriate, spam, etc.)                      |         |          |
| hasInappropriateContent | Boolean | Flag for inappropriate content                              |   X   |          |
| requiresManualReview | Boolean   | Flag for manual review                                       |   X   |          |
| isAutoModerated | Boolean       | Flag for automatic moderation                                 |         |          |
| metadata       | Object        | Additional context data                                        |         |          |
| createdAt      | Date          | Creation timestamp                                             |   X   |    X     |
| updatedAt      | Date          | Last update timestamp                                          |   X   |    X     |

## Indexing Strategy

* **Compound index:** `contentType`, `status`, `priority` (for moderation queue)
* **Compound index:** `moderatorId`, `status` (for moderator's assigned items)
* **Compound index:** `contentId`, `contentType` (for content-specific moderation)
* **Compound index:** `hasInappropriateContent`, `requiresManualReview` (for filtering)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "contentType": "JobPosting",
  "contentId": ObjectId("..."),
  "moderatorId": ObjectId("..."),
  "status": "Pending",
  "priority": "High",
  "reason": "Automated flag - potential inappropriate content",
  "notes": "Contains potentially discriminatory language",
  "flags": ["discriminatory_language", "salary_missing"],
  "hasInappropriateContent": true,
  "requiresManualReview": true,
  "isAutoModerated": false,
  "metadata": {
    "reportedBy": ObjectId("..."),
    "automatedScanScore": 0.85,
    "flaggedKeywords": ["discriminatory_term_1", "discriminatory_term_2"]
  },
  "createdAt": ISODate("2024-03-20T10:00:00Z"),
  "updatedAt": ISODate("2024-03-20T10:00:00Z")
}
``` 