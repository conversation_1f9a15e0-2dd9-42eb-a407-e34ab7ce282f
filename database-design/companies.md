# Collection: companies

## Description

Stores detailed company profiles and verification information.

## Schema

| Field                    | Type          | Description                                                    | Indexed | Required |
| ----------------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id                     | ObjectId      | Unique identifier for the company                              |   X   |    X     |
| name                    | String        | Company name                                                   |   X   |    X     |
| companyType             | String        | Type (Government, Private, Multinational)                      |   X   |    X     |
| industry               | String        | Industry sector                                                |   X   |    X     |
| companySize            | String        | Size range (e.g., "1-10", "11-50", "51-200", "201-500")       |   X   |          |
| foundedYear            | Number        | Year company was founded                                       |         |          |
| description            | String        | Company description                                            |         |    X     |
| website                | String        | Company website URL                                            |         |          |
| logo                   | String        | URL to company logo                                            |         |          |
| headquarters          | String        | Company headquarters location                                   |   X   |    X     |
| locations             | [String]      | Office locations                                                |   X   |          |
| socialMediaLinks      | Object        | Social media profile URLs                                       |         |          |
| benefits              | [String]      | Company benefits                                                |         |          |
| culture               | String        | Company culture description                                     |         |          |
| verificationStatus    | String        | Verification status (pending, verified, rejected)               |   X   |    X     |
| verificationDate      | Date          | Date when verification was completed                            |         |          |
| businessRegNumber     | String        | Business registration number                                    |   X   |          |
| taxId                 | String        | Tax identification number                                       |         |          |
| contactEmail          | String        | Primary contact email                                          |   X   |    X     |
| contactPhone          | String        | Primary contact phone                                          |         |    X     |
| createdAt             | Date          | Profile creation timestamp                                      |   X   |    X     |
| updatedAt             | Date          | Last profile update timestamp                                   |   X   |    X     |

## Social Media Links Sub-document Schema

```json
{
  "linkedin": String,
  "twitter": String,
  "facebook": String,
  "instagram": String,
  "glassdoor": String
}
```

## Indexing Strategy

* **Text index:** `name`, `description` (for company search)
* **Compound index:** `industry`, `companyType`, `companySize` (for company filtering)
* **Compound index:** `verificationStatus`, `verificationDate` (for verification management)
* **Compound index:** `headquarters`, `industry` (for location-based company search)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "name": "Tech Innovators Inc.",
  "companyType": "Private",
  "industry": "Technology",
  "companySize": "51-200",
  "foundedYear": 2015,
  "description": "Leading technology solutions provider...",
  "website": "https://techinnovators.com",
  "logo": "https://storage.example.com/logos/tech-innovators.png",
  "headquarters": "San Francisco, CA",
  "locations": ["San Francisco", "New York", "London"],
  "socialMediaLinks": {
    "linkedin": "https://linkedin.com/company/tech-innovators",
    "twitter": "https://twitter.com/techinnovators"
  },
  "benefits": [
    "Health Insurance",
    "401(k) Matching",
    "Remote Work Options"
  ],
  "culture": "We foster innovation and collaboration...",
  "verificationStatus": "verified",
  "verificationDate": ISODate("2024-02-15"),
  "businessRegNumber": "BRN123456789",
  "taxId": "TAX987654321",
  "contactEmail": "<EMAIL>",
  "contactPhone": "******-0123",
  "createdAt": ISODate("2024-01-01"),
  "updatedAt": ISODate("2024-03-20")
}
```