# Collection: company_responses

## Description

Stores official company responses to reviews and feedback.

## Schema

| Field           | Type          | Description                                                    | Indexed | Required |
| -------------- | ------------- | -------------------------------------------------------------- | :-----: | :------: |
| _id            | ObjectId      | Unique identifier for the response                             |   X   |    X     |
| reviewId       | ObjectId      | Reference to the company review                                |   X   |    X     |
| responderId    | ObjectId      | Reference to company representative                            |   X   |    X     |
| content        | String        | Response text                                                  |         |    X     |
| position       | String        | Responder's position                                           |         |    X     |
| department     | String        | Responder's department                                         |         |    X     |
| isOfficial     | Boolean       | Official response flag                                         |   X   |    X     |
| isPublic       | Boolean       | Public visibility flag                                         |   X   |    X     |
| status         | String        | Response status (draft, published, archived)                   |   X   |    X     |
| moderationStatus| String       | Moderation status (pending, approved, rejected)                |   X   |          |
| lastModifiedBy | ObjectId      | Last modifier's ID                                            |         |          |
| version        | Number        | Response version                                               |         |    X     |
| isPinned       | Boolean       | Pinned status                                                 |   X   |          |
| metadata       | Object        | Additional response metadata                                   |         |          |
| createdAt      | Date          | Response creation timestamp                                    |   X   |    X     |
| updatedAt      | Date          | Last response update timestamp                                 |   X   |    X     |
| publishedAt    | Date          | Response publication timestamp                                 |   X   |          |

## Metadata Sub-document Schema

```json
{
  "reviewRating": Number,
  "reviewDate": Date,
  "reviewCategory": String,
  "approvedBy": ObjectId,
  "approvalDate": Date,
  "editHistory": [
    {
      "editorId": ObjectId,
      "editDate": Date,
      "changes": [String]
    }
  ]
}
```

## Indexing Strategy

* **Compound index:** `reviewId`, `isOfficial` (for official responses)
* **Compound index:** `responderId`, `status` (for responder's drafts)
* **Compound index:** `moderationStatus`, `createdAt` (for moderation queue)
* **Text index:** `content` (for response search)

## Sample Document

```json
{
  "_id": ObjectId("..."),
  "reviewId": ObjectId("..."),
  "responderId": ObjectId("..."),
  "content": "Thank you for your detailed feedback. We appreciate...",
  "position": "HR Director",
  "department": "Human Resources",
  "isOfficial": true,
  "isPublic": true,
  "status": "published",
  "moderationStatus": "approved",
  "lastModifiedBy": ObjectId("..."),
  "version": 1,
  "isPinned": true,
  "metadata": {
    "reviewRating": 3,
    "reviewDate": ISODate("2024-03-15"),
    "reviewCategory": "work_culture",
    "approvedBy": ObjectId("..."),
    "approvalDate": ISODate("2024-03-20"),
    "editHistory": [
      {
        "editorId": ObjectId("..."),
        "editDate": ISODate("2024-03-19"),
        "changes": ["initial_draft"]
      }
    ]
  },
  "createdAt": ISODate("2024-03-19"),
  "updatedAt": ISODate("2024-03-20"),
  "publishedAt": ISODate("2024-03-20")
}
``` 