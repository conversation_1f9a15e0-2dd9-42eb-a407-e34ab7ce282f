// Application Constants
export const APP_NAME = 'Jobs Portal Qatar';
export const APP_DESCRIPTION = 'Find your dream job in Qatar';
export const APP_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';

// API Routes
export const API_ROUTES = {
  AUTH: {
    LOGIN: '/api/auth/login',
    REGISTER: '/api/auth/register',
    LOGOUT: '/api/auth/logout',
    PROFILE: '/api/auth/profile',
  },
  USERS: {
    BASE: '/api/users',
    PROFILE: '/api/users/profile',
  },
  JOBS: {
    BASE: '/api/jobs',
    SEARCH: '/api/jobs/search',
    APPLY: '/api/jobs/apply',
    SAVED: '/api/jobs/saved',
  },
  APPLICATIONS: {
    BASE: '/api/applications',
  },
} as const;

// Page Routes
export const ROUTES = {
  HOME: '/',
  AUTH: {
    SIGN_IN: '/auth/sign-in',
    SIGN_UP: '/auth/sign-up',
  },
  DASHBOARD: '/dashboard',
  PROFILE: '/profile',
  JOBS: {
    SEARCH: '/jobs',
    DETAILS: '/jobs/[id]',
    POST: '/jobs/post',
    MANAGE: '/jobs/manage',
  },
  APPLICATIONS: '/applications',
  SAVED_JOBS: '/saved-jobs',
  ONBOARDING: '/onboarding',
} as const;

// Job Types and Industries
export const JOB_TYPES = [
  { value: 'FULL_TIME', label: 'Full Time' },
  { value: 'PART_TIME', label: 'Part Time' },
  { value: 'CONTRACT', label: 'Contract' },
  { value: 'FREELANCE', label: 'Freelance' },
  { value: 'INTERNSHIP', label: 'Internship' },
] as const;

export const INDUSTRIES = [
  'Technology',
  'Healthcare',
  'Finance',
  'Education',
  'Construction',
  'Oil & Gas',
  'Hospitality',
  'Retail',
  'Manufacturing',
  'Transportation',
  'Government',
  'Non-Profit',
  'Other',
] as const;

export const EXPERIENCE_LEVELS = [
  'Entry Level',
  '1-2 years',
  '3-5 years',
  '5-10 years',
  '10+ years',
] as const;

export const EDUCATION_LEVELS = [
  'High School',
  'Associate Degree',
  'Bachelor\'s Degree',
  'Master\'s Degree',
  'PhD',
  'Professional Certification',
] as const;

// Qatar Specific Data
export const QATAR_CITIES = [
  'Doha',
  'Al Rayyan',
  'Al Wakrah',
  'Al Khor',
  'Dukhan',
  'Mesaieed',
  'Ras Laffan',
  'Al Shamal',
] as const;

export const CURRENCIES = [
  { code: 'QAR', symbol: 'ر.ق', name: 'Qatari Riyal' },
  { code: 'USD', symbol: '$', name: 'US Dollar' },
  { code: 'EUR', symbol: '€', name: 'Euro' },
] as const;

// Validation Constants
export const VALIDATION = {
  PASSWORD_MIN_LENGTH: 8,
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 50,
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE_REGEX: /^\+?[1-9]\d{1,14}$/,
  FILE_SIZE_LIMIT: 5 * 1024 * 1024, // 5MB
  ALLOWED_FILE_TYPES: ['pdf', 'doc', 'docx'],
  IMAGE_FILE_TYPES: ['jpg', 'jpeg', 'png', 'webp'],
} as const;

// UI Constants
export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  '2XL': 1536,
} as const;

export const ANIMATION_DURATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
} as const;

// Pagination
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100,
  JOBS_PER_PAGE: 12,
  APPLICATIONS_PER_PAGE: 10,
} as const;

// Date Formats
export const DATE_FORMATS = {
  DISPLAY: 'MMM dd, yyyy',
  INPUT: 'yyyy-MM-dd',
  FULL: 'MMMM dd, yyyy HH:mm',
  TIME: 'HH:mm',
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  GENERIC: 'Something went wrong. Please try again.',
  NETWORK: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  NOT_FOUND: 'The requested resource was not found.',
  VALIDATION: 'Please check your input and try again.',
  FILE_TOO_LARGE: 'File size exceeds the maximum limit.',
  INVALID_FILE_TYPE: 'Invalid file type. Please upload a supported file.',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  PROFILE_UPDATED: 'Profile updated successfully!',
  APPLICATION_SUBMITTED: 'Application submitted successfully!',
  JOB_SAVED: 'Job saved to your favorites!',
  JOB_POSTED: 'Job posted successfully!',
  PASSWORD_CHANGED: 'Password changed successfully!',
} as const;

// Feature Flags
export const FEATURES = {
  ENABLE_NOTIFICATIONS: true,
  ENABLE_CHAT: true,
  ENABLE_VIDEO_INTERVIEWS: false,
  ENABLE_AI_MATCHING: false,
  ENABLE_PREMIUM_FEATURES: true,
} as const;

// Social Media Links
export const SOCIAL_LINKS = {
  FACEBOOK: 'https://facebook.com/jobsportalqatar',
  TWITTER: 'https://twitter.com/jobsportalqatar',
  LINKEDIN: 'https://linkedin.com/company/jobsportalqatar',
  INSTAGRAM: 'https://instagram.com/jobsportalqatar',
} as const;
