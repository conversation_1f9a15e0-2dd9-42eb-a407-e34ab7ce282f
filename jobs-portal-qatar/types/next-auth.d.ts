import { UserRole } from './index';

declare module 'next-auth' {
  /**
   * Extends the built-in session types to include custom properties
   */
  interface Session {
    user: {
      id: string;
      email: string;
      name: string;
      role: UserRole;
    };
  }

  /**
   * Extends the built-in user types to include custom properties
   */
  interface User {
    id: string;
    email: string;
    name: string;
    role: UserRole;
  }
}

declare module 'next-auth/adapters' {
  /**
   * Extends the built-in adapter user types to include custom properties
   */
  interface AdapterUser {
    id: string;
    email: string;
    name: string;
    role: UserRole;
    emailVerified: Date | null;
  }
}

declare module 'next-auth/jwt' {
  /**
   * Extends the built-in JWT types to include custom properties
   */
  interface JWT {
    id: string;
    role: UserRole;
  }
}
