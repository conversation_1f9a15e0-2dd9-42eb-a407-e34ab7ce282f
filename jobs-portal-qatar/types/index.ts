// User and Authentication Types
export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  emailVerified: boolean;
  mobileNumber?: string;
  createdAt: Date;
  updatedAt: Date;
  profile?: Profile;
}

export enum UserRole {
  JOB_SEEKER = 'JOB_SEEKER',
  EMPLOYER = 'EMPLOYER',
  ADMIN = 'ADMIN',
}

export interface Profile {
  id: string;
  userId: string;
  dateOfBirth?: Date;
  nationality?: string;
  address?: string;
  skills: string[];
  education: Education[];
  workExperience: WorkExperience[];
  resumeUrl?: string;
  profilePicture?: string;
  desiredJobType?: string;
  desiredIndustry?: string;
  desiredLocation?: string;
  salaryExpectation?: number;
  visibility: ProfileVisibility;
  createdAt: Date;
  updatedAt: Date;
}

export enum ProfileVisibility {
  PUBLIC = 'PUBLIC',
  PRIVATE = 'PRIVATE',
  PARTIAL = 'PARTIAL',
}

export interface Education {
  id: string;
  profileId: string;
  institution: string;
  degree: string;
  fieldOfStudy: string;
  startDate: Date;
  endDate?: Date;
  grade?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface WorkExperience {
  id: string;
  profileId: string;
  company: string;
  position: string;
  location?: string;
  startDate: Date;
  endDate?: Date;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Job Related Types
export interface JobPost {
  id: string;
  employerId: string;
  employer: User;
  title: string;
  description: string;
  requirements: string[];
  responsibilities: string[];
  location: string;
  salary?: Salary;
  jobType: JobType;
  industry: string;
  experienceLevel: string;
  education?: string;
  deadline?: Date;
  status: JobStatus;
  applications: JobApplication[];
  savedBy: SavedJob[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Salary {
  min: number;
  max: number;
  currency: string;
}

export enum JobType {
  FULL_TIME = 'FULL_TIME',
  PART_TIME = 'PART_TIME',
  CONTRACT = 'CONTRACT',
  FREELANCE = 'FREELANCE',
  INTERNSHIP = 'INTERNSHIP',
}

export enum JobStatus {
  ACTIVE = 'ACTIVE',
  CLOSED = 'CLOSED',
  DRAFT = 'DRAFT',
}

export interface JobApplication {
  id: string;
  jobId: string;
  job: JobPost;
  applicantId: string;
  applicant: User;
  resumeUrl: string;
  coverLetter?: string;
  status: ApplicationStatus;
  createdAt: Date;
  updatedAt: Date;
}

export enum ApplicationStatus {
  PENDING = 'PENDING',
  REVIEWED = 'REVIEWED',
  SHORTLISTED = 'SHORTLISTED',
  REJECTED = 'REJECTED',
  ACCEPTED = 'ACCEPTED',
}

export interface SavedJob {
  id: string;
  jobId: string;
  job: JobPost;
  userId: string;
  user: User;
  createdAt: Date;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form Types
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  role: UserRole;
}

export interface JobSearchFilters {
  query?: string;
  location?: string;
  jobType?: JobType[];
  industry?: string[];
  experienceLevel?: string[];
  salaryRange?: {
    min?: number;
    max?: number;
  };
  datePosted?: 'today' | 'week' | 'month' | 'all';
}

// Component Props Types
export interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
}

export interface InputProps {
  label?: string;
  placeholder?: string;
  type?: string;
  value?: string;
  onChange?: (value: string) => void;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  className?: string;
}

// Utility Types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};
