import Stripe from 'stripe';

// Initialize Stripe
export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-06-30.basil',
});

// Stripe configuration
export const stripeConfig = {
  publishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!,
  secretKey: process.env.STRIPE_SECRET_KEY!,
  webhookSecret: process.env.STRIPE_WEBHOOK_SECRET!,
  currency: 'qar',
  country: 'QA',
};

// Product and pricing configuration
export const PRICING_PLANS = {
  BASIC_JOB_POST: {
    name: 'Basic Job Post',
    description: 'Post a single job for 30 days',
    price: 500, // 500 QAR
    currency: 'qar',
    duration: 30, // days
    features: [
      '30-day job posting',
      'Basic applicant filtering',
      'Email notifications',
      'Standard support',
    ],
  },
  PREMIUM_JOB_POST: {
    name: 'Premium Job Post',
    description: 'Featured job post with enhanced visibility',
    price: 1000, // 1000 QAR
    currency: 'qar',
    duration: 30, // days
    features: [
      '30-day featured job posting',
      'Priority placement in search',
      'Advanced applicant filtering',
      'Detailed analytics',
      'Priority support',
    ],
  },
  EMPLOYER_MONTHLY: {
    name: 'Employer Monthly Plan',
    description: 'Unlimited job postings for one month',
    price: 2500, // 2500 QAR
    currency: 'qar',
    duration: 30, // days
    features: [
      'Unlimited job postings',
      'Company profile page',
      'Advanced analytics',
      'Bulk applicant management',
      'Priority support',
      'Custom branding',
    ],
  },
  EMPLOYER_YEARLY: {
    name: 'Employer Yearly Plan',
    description: 'Unlimited job postings for one year',
    price: 25000, // 25000 QAR (save 2 months)
    currency: 'qar',
    duration: 365, // days
    features: [
      'Unlimited job postings',
      'Company profile page',
      'Advanced analytics',
      'Bulk applicant management',
      'Priority support',
      'Custom branding',
      'Dedicated account manager',
      'API access',
    ],
  },
};

// Create payment intent
export const createPaymentIntent = async (
  amount: number,
  currency: string = 'qar',
  metadata?: Record<string, string>
) => {
  try {
    const paymentIntent = await stripe.paymentIntents.create({
      amount: amount * 100, // Convert to smallest currency unit
      currency,
      ...(metadata && { metadata }),
      automatic_payment_methods: {
        enabled: true,
      },
    });

    return {
      success: true,
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id,
    };
  } catch (error) {
    console.error('Error creating payment intent:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

// Create subscription
export const createSubscription = async (
  customerId: string,
  priceId: string,
  metadata?: Record<string, string>
) => {
  try {
    const subscription = await stripe.subscriptions.create({
      customer: customerId,
      items: [{ price: priceId }],
      ...(metadata && { metadata }),
      payment_behavior: 'default_incomplete',
      payment_settings: { save_default_payment_method: 'on_subscription' },
      expand: ['latest_invoice.payment_intent'],
    });

    let clientSecret: string | undefined;

    if (
      subscription.latest_invoice &&
      typeof subscription.latest_invoice === 'object'
    ) {
      const invoice = subscription.latest_invoice as any;
      if (
        invoice.payment_intent &&
        typeof invoice.payment_intent === 'object'
      ) {
        const paymentIntent = invoice.payment_intent as Stripe.PaymentIntent;
        clientSecret = paymentIntent.client_secret || undefined;
      }
    }

    return {
      success: true,
      subscription,
      clientSecret,
    };
  } catch (error) {
    console.error('Error creating subscription:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

// Create customer
export const createCustomer = async (
  email: string,
  name: string,
  metadata?: Record<string, string>
) => {
  try {
    const customer = await stripe.customers.create({
      email,
      name,
      ...(metadata && { metadata }),
    });

    return {
      success: true,
      customer,
    };
  } catch (error) {
    console.error('Error creating customer:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

// Get customer
export const getCustomer = async (customerId: string) => {
  try {
    const customer = await stripe.customers.retrieve(customerId);
    return {
      success: true,
      customer,
    };
  } catch (error) {
    console.error('Error retrieving customer:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

// Cancel subscription
export const cancelSubscription = async (subscriptionId: string) => {
  try {
    const subscription = await stripe.subscriptions.cancel(subscriptionId);
    return {
      success: true,
      subscription,
    };
  } catch (error) {
    console.error('Error canceling subscription:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

// Construct webhook event
export const constructWebhookEvent = (
  payload: string | Buffer,
  signature: string
) => {
  try {
    const event = stripe.webhooks.constructEvent(
      payload,
      signature,
      stripeConfig.webhookSecret
    );
    return { success: true, event };
  } catch (error) {
    console.error('Error constructing webhook event:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

// Format amount for display
export const formatAmount = (amount: number, currency: string = 'qar') => {
  return new Intl.NumberFormat('en-QA', {
    style: 'currency',
    currency: currency.toUpperCase(),
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};
