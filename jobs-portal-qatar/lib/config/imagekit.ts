import ImageKit from 'imagekit';

// ImageKit configuration
export const imagekitConfig = {
  publicKey: process.env.NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY!,
  privateKey: process.env.IMAGEKIT_PRIVATE_KEY!,
  urlEndpoint: process.env.NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT!,
};

// Initialize ImageKit instance
export const imagekit = new ImageKit(imagekitConfig);

// File upload options
export const uploadOptions = {
  resumes: {
    folder: '/resumes',
    allowedFormats: ['pdf', 'doc', 'docx'],
    maxSize: 5 * 1024 * 1024, // 5MB
    tags: ['resume', 'document'],
  },
  profilePictures: {
    folder: '/profile-pictures',
    allowedFormats: ['jpg', 'jpeg', 'png', 'webp'],
    maxSize: 2 * 1024 * 1024, // 2MB
    tags: ['profile', 'image'],
    transformation: [
      {
        height: 400,
        width: 400,
        crop: 'maintain_ratio',
        quality: 80,
      },
    ],
  },
  companyLogos: {
    folder: '/company-logos',
    allowedFormats: ['jpg', 'jpeg', 'png', 'webp', 'svg'],
    maxSize: 1 * 1024 * 1024, // 1MB
    tags: ['company', 'logo'],
    transformation: [
      {
        height: 200,
        width: 200,
        crop: 'maintain_ratio',
        quality: 90,
      },
    ],
  },
  companyCoverImages: {
    folder: '/company-covers',
    allowedFormats: ['jpg', 'jpeg', 'png', 'webp'],
    maxSize: 3 * 1024 * 1024, // 3MB
    tags: ['company', 'cover'],
    transformation: [
      {
        height: 400,
        width: 1200,
        crop: 'maintain_ratio',
        quality: 85,
      },
    ],
  },
};

// Helper functions
export const generateUploadSignature = (fileName: string, folder: string) => {
  const token = imagekit.getAuthenticationParameters();
  return {
    signature: token.signature,
    expire: token.expire,
    token: token.token,
    fileName,
    folder,
  };
};

export const deleteFile = async (fileId: string) => {
  try {
    await imagekit.deleteFile(fileId);
    return { success: true };
  } catch (error) {
    console.error('Error deleting file from ImageKit:', error);
    return { success: false, error };
  }
};

export const getFileDetails = async (fileId: string) => {
  try {
    const fileDetails = await imagekit.getFileDetails(fileId);
    return { success: true, data: fileDetails };
  } catch (error) {
    console.error('Error getting file details from ImageKit:', error);
    return { success: false, error };
  }
};

// URL generation helpers
export const generateImageUrl = (
  path: string,
  transformations?: Array<{
    height?: number;
    width?: number;
    crop?: string;
    quality?: number;
    format?: string;
  }>
) => {
  return imagekit.url({
    path,
    ...(transformations && { transformation: transformations }),
  });
};

export const generateThumbnail = (path: string, size: number = 150) => {
  return imagekit.url({
    path,
    transformation: [
      {
        height: size,
        width: size,
        crop: 'maintain_ratio',
        quality: 80,
      },
    ],
  });
};
