// Export all configuration modules
export * from './imagekit';
export * from './firebase';
export * from './stripe';
export * from './sendgrid';
export * from './algolia';

// Environment validation
export const validateEnvironment = () => {
  const requiredEnvVars = [
    'DATABASE_URL',
    'NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY',
    'CLERK_SECRET_KEY',
    'NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY',
    'IMAGEKIT_PRIVATE_KEY',
    'NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT',
    'NEXT_PUBLIC_FIREBASE_API_KEY',
    'NEXT_PUBLIC_FIREBASE_PROJECT_ID',
    'STRIPE_SECRET_KEY',
    'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY',
    'SENDGRID_API_KEY',
    'NEXT_PUBLIC_ALGOLIA_APP_ID',
    'ALGOLIA_ADMIN_API_KEY',
  ];

  const missingVars = requiredEnvVars.filter(
    (envVar) => !process.env[envVar]
  );

  if (missingVars.length > 0) {
    console.error('Missing required environment variables:', missingVars);
    if (process.env.NODE_ENV === 'production') {
      throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
    }
  }

  return {
    isValid: missingVars.length === 0,
    missingVars,
  };
};

// Application configuration
export const appConfig = {
  name: 'Jobs Portal Qatar',
  description: 'Find your dream job in Qatar',
  url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  version: process.env.npm_package_version || '1.0.0',
  environment: process.env.NODE_ENV || 'development',
  
  // Feature flags
  features: {
    enableNotifications: process.env.ENABLE_NOTIFICATIONS === 'true',
    enableChat: process.env.ENABLE_CHAT === 'true',
    enableVideoInterviews: process.env.ENABLE_VIDEO_INTERVIEWS === 'true',
    enableAIMatching: process.env.ENABLE_AI_MATCHING === 'true',
    enablePremiumFeatures: process.env.ENABLE_PREMIUM_FEATURES === 'true',
  },
  
  // Rate limiting
  rateLimiting: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100, // limit each IP to 100 requests per windowMs
  },
  
  // File upload limits
  fileUpload: {
    maxSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'webp'],
  },
  
  // Pagination
  pagination: {
    defaultPageSize: 10,
    maxPageSize: 100,
    jobsPerPage: 12,
    applicationsPerPage: 10,
  },
  
  // Cache settings
  cache: {
    defaultTTL: 60 * 60, // 1 hour in seconds
    jobsTTL: 30 * 60, // 30 minutes
    companiesTTL: 60 * 60, // 1 hour
    profilesTTL: 15 * 60, // 15 minutes
  },
};
