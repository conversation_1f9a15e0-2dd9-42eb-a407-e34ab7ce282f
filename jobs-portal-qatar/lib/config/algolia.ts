import { algoliasearch } from 'algoliasearch';

// Initialize Algolia client
const client = algoliasearch(
  process.env.NEXT_PUBLIC_ALGOLIA_APP_ID!,
  process.env.ALGOLIA_ADMIN_API_KEY!
);

// Search client for frontend
export const searchClient = algoliasearch(
  process.env.NEXT_PUBLIC_ALGOLIA_APP_ID!,
  process.env.NEXT_PUBLIC_ALGOLIA_SEARCH_API_KEY!
);

// Index names
export const ALGOLIA_INDEXES = {
  JOBS: 'jobs',
  COMPANIES: 'companies',
  PROFILES: 'profiles',
} as const;

// Initialize indexes
export const jobsIndex = client.initIndex({ indexName: ALGOLIA_INDEXES.JOBS });
export const companiesIndex = client.initIndex({ indexName: ALGOLIA_INDEXES.COMPANIES });
export const profilesIndex = client.initIndex({ indexName: ALGOLIA_INDEXES.PROFILES });

// Search indexes for frontend
export const searchJobsIndex = searchClient.initIndex({ indexName: ALGOLIA_INDEXES.JOBS });
export const searchCompaniesIndex = searchClient.initIndex({ indexName: ALGOLIA_INDEXES.COMPANIES });
export const searchProfilesIndex = searchClient.initIndex({ indexName: ALGOLIA_INDEXES.PROFILES });

// Index configuration
export const configureIndexes = async () => {
  try {
    // Configure jobs index
    await jobsIndex.setSettings({
      searchableAttributes: [
        'title',
        'description',
        'requirements',
        'responsibilities',
        'company.name',
        'location',
        'industry',
        'skills',
      ],
      attributesForFaceting: [
        'jobType',
        'workType',
        'experienceLevel',
        'industry',
        'location',
        'company.name',
        'salary.currency',
        'status',
        'featured',
      ],
      customRanking: [
        'desc(featured)',
        'desc(publishedAt)',
        'desc(views)',
      ],
      attributesToRetrieve: [
        'objectID',
        'title',
        'description',
        'company',
        'location',
        'salary',
        'jobType',
        'workType',
        'experienceLevel',
        'industry',
        'publishedAt',
        'deadline',
        'featured',
        'urgent',
        'slug',
      ],
      attributesToHighlight: [
        'title',
        'description',
        'company.name',
      ],
      attributesToSnippet: [
        'description:50',
        'requirements:30',
      ],
      hitsPerPage: 20,
      maxValuesPerFacet: 100,
    });

    // Configure companies index
    await companiesIndex.setSettings({
      searchableAttributes: [
        'name',
        'description',
        'industry',
        'locations',
        'benefits',
        'culture',
      ],
      attributesForFaceting: [
        'industry',
        'companySize',
        'locations',
        'verificationStatus',
      ],
      customRanking: [
        'desc(verificationStatus)',
        'desc(jobPostsCount)',
      ],
      attributesToRetrieve: [
        'objectID',
        'name',
        'slug',
        'description',
        'industry',
        'companySize',
        'logo',
        'locations',
        'verificationStatus',
        'jobPostsCount',
      ],
      attributesToHighlight: [
        'name',
        'description',
      ],
      attributesToSnippet: [
        'description:100',
      ],
      hitsPerPage: 12,
    });

    // Configure profiles index
    await profilesIndex.setSettings({
      searchableAttributes: [
        'user.name',
        'bio',
        'skills',
        'languages',
        'education.degree',
        'education.fieldOfStudy',
        'workExperience.position',
        'workExperience.company',
        'desiredIndustry',
        'desiredJobType',
      ],
      attributesForFaceting: [
        'skills',
        'languages',
        'desiredIndustry',
        'desiredJobType',
        'city',
        'country',
        'nationality',
        'visibility',
      ],
      customRanking: [
        'desc(isProfileComplete)',
        'desc(user.lastLogin)',
      ],
      attributesToRetrieve: [
        'objectID',
        'userId',
        'user.name',
        'bio',
        'skills',
        'languages',
        'city',
        'country',
        'desiredIndustry',
        'desiredJobType',
        'profilePicture',
        'isProfileComplete',
      ],
      attributesToHighlight: [
        'user.name',
        'bio',
        'skills',
      ],
      attributesToSnippet: [
        'bio:100',
      ],
      hitsPerPage: 15,
    });

    console.log('Algolia indexes configured successfully');
  } catch (error) {
    console.error('Error configuring Algolia indexes:', error);
  }
};

// Helper functions for indexing
export const indexJob = async (job: any) => {
  try {
    const jobData = {
      objectID: job.id,
      ...job,
      _tags: [
        `jobType:${job.jobType}`,
        `workType:${job.workType}`,
        `experienceLevel:${job.experienceLevel}`,
        `industry:${job.industry}`,
        `location:${job.location}`,
        ...(job.featured ? ['featured'] : []),
        ...(job.urgent ? ['urgent'] : []),
      ],
    };

    await jobsIndex.saveObject({ body: jobData });
    console.log(`Job ${job.id} indexed successfully`);
  } catch (error) {
    console.error(`Error indexing job ${job.id}:`, error);
  }
};

export const indexCompany = async (company: any) => {
  try {
    const companyData = {
      objectID: company.id,
      ...company,
      _tags: [
        `industry:${company.industry}`,
        `companySize:${company.companySize}`,
        `verificationStatus:${company.verificationStatus}`,
        ...company.locations.map((location: string) => `location:${location}`),
      ],
    };

    await companiesIndex.saveObject(companyData);
    console.log(`Company ${company.id} indexed successfully`);
  } catch (error) {
    console.error(`Error indexing company ${company.id}:`, error);
  }
};

export const indexProfile = async (profile: any) => {
  try {
    if (profile.visibility !== 'PUBLIC') {
      // Remove from index if not public
      await profilesIndex.deleteObject(profile.id);
      return;
    }

    const profileData = {
      objectID: profile.id,
      ...profile,
      _tags: [
        ...profile.skills.map((skill: string) => `skill:${skill}`),
        ...profile.languages.map((language: string) => `language:${language}`),
        `city:${profile.city}`,
        `country:${profile.country}`,
        `desiredIndustry:${profile.desiredIndustry}`,
        `desiredJobType:${profile.desiredJobType}`,
      ],
    };

    await profilesIndex.saveObject(profileData);
    console.log(`Profile ${profile.id} indexed successfully`);
  } catch (error) {
    console.error(`Error indexing profile ${profile.id}:`, error);
  }
};

// Delete from index
export const deleteFromIndex = async (indexName: string, objectId: string) => {
  try {
    const index = client.initIndex({ indexName });
    await index.deleteObject({ objectID: objectId });
    console.log(`Object ${objectId} deleted from ${indexName} index`);
  } catch (error) {
    console.error(`Error deleting object ${objectId} from ${indexName}:`, error);
  }
};

// Batch operations
export const batchIndexJobs = async (jobs: any[]) => {
  try {
    const objects = jobs.map(job => ({
      objectID: job.id,
      ...job,
      _tags: [
        `jobType:${job.jobType}`,
        `workType:${job.workType}`,
        `experienceLevel:${job.experienceLevel}`,
        `industry:${job.industry}`,
        `location:${job.location}`,
        ...(job.featured ? ['featured'] : []),
        ...(job.urgent ? ['urgent'] : []),
      ],
    }));

    await jobsIndex.saveObjects(objects);
    console.log(`${jobs.length} jobs indexed successfully`);
  } catch (error) {
    console.error('Error batch indexing jobs:', error);
  }
};

export { client as algoliaClient };
