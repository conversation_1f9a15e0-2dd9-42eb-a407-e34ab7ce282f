import sgMail from '@sendgrid/mail';

// Initialize SendGrid
sgMail.setApiKey(process.env.SENDGRID_API_KEY!);

// Email configuration
export const emailConfig = {
  apiKey: process.env.SENDGRID_API_KEY!,
  fromEmail: process.env.SENDGRID_FROM_EMAIL || '<EMAIL>',
  fromName: 'Jobs Portal Qatar',
};

// Email templates
export const EMAIL_TEMPLATES = {
  WELCOME: {
    templateId: 'd-welcome-template-id',
    subject: 'Welcome to Jobs Portal Qatar',
  },
  JOB_APPLICATION_CONFIRMATION: {
    templateId: 'd-job-application-confirmation',
    subject: 'Application Submitted Successfully',
  },
  APPLICATION_STATUS_UPDATE: {
    templateId: 'd-application-status-update',
    subject: 'Application Status Update',
  },
  NEW_JOB_ALERT: {
    templateId: 'd-new-job-alert',
    subject: 'New Job Opportunities Match Your Preferences',
  },
  PASSWORD_RESET: {
    templateId: 'd-password-reset',
    subject: 'Reset Your Password',
  },
  INTERVIEW_INVITATION: {
    templateId: 'd-interview-invitation',
    subject: 'Interview Invitation',
  },
  JOB_OFFER: {
    templateId: 'd-job-offer',
    subject: 'Job Offer',
  },
  EMPLOYER_WELCOME: {
    templateId: 'd-employer-welcome',
    subject: 'Welcome to Jobs Portal Qatar - Employer',
  },
  JOB_POST_APPROVED: {
    templateId: 'd-job-post-approved',
    subject: 'Your Job Post Has Been Approved',
  },
  JOB_POST_EXPIRED: {
    templateId: 'd-job-post-expired',
    subject: 'Your Job Post Has Expired',
  },
};

// Send email function
export const sendEmail = async (
  to: string | string[],
  templateId: string,
  dynamicTemplateData: Record<string, any>,
  subject?: string,
  from?: string
) => {
  try {
    const msg = {
      to: Array.isArray(to) ? to : [to],
      from: from || emailConfig.fromEmail,
      templateId,
      dynamicTemplateData: {
        ...dynamicTemplateData,
        app_name: 'Jobs Portal Qatar',
        app_url: process.env.NEXT_PUBLIC_APP_URL,
        support_email: emailConfig.fromEmail,
      },
      ...(subject && { subject }),
    };

    const response = await sgMail.send(msg);
    console.log('Email sent successfully:', response[0].statusCode);

    return {
      success: true,
      messageId: response[0].headers['x-message-id'],
    };
  } catch (error) {
    console.error('Error sending email:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

// Send bulk emails
export const sendBulkEmails = async (
  emails: Array<{
    to: string;
    templateId: string;
    dynamicTemplateData: Record<string, any>;
    subject?: string;
  }>
) => {
  try {
    const messages = emails.map(email => ({
      to: email.to,
      from: emailConfig.fromEmail,
      templateId: email.templateId,
      dynamicTemplateData: {
        ...email.dynamicTemplateData,
        app_name: 'Jobs Portal Qatar',
        app_url: process.env.NEXT_PUBLIC_APP_URL,
        support_email: emailConfig.fromEmail,
      },
      ...(email.subject && { subject: email.subject }),
    }));

    await sgMail.send(messages);
    console.log('Bulk emails sent successfully');

    return {
      success: true,
      count: messages.length,
    };
  } catch (error) {
    console.error('Error sending bulk emails:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

// Email helper functions
export const sendWelcomeEmail = async (
  email: string,
  name: string,
  role: 'job_seeker' | 'employer'
) => {
  const templateId =
    role === 'employer'
      ? EMAIL_TEMPLATES.EMPLOYER_WELCOME.templateId
      : EMAIL_TEMPLATES.WELCOME.templateId;

  return sendEmail(email, templateId, {
    name,
    role,
    dashboard_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard`,
  });
};

export const sendJobApplicationConfirmation = async (
  email: string,
  applicantName: string,
  jobTitle: string,
  companyName: string
) => {
  return sendEmail(
    email,
    EMAIL_TEMPLATES.JOB_APPLICATION_CONFIRMATION.templateId,
    {
      applicant_name: applicantName,
      job_title: jobTitle,
      company_name: companyName,
      applications_url: `${process.env.NEXT_PUBLIC_APP_URL}/applications`,
    }
  );
};

export const sendApplicationStatusUpdate = async (
  email: string,
  applicantName: string,
  jobTitle: string,
  companyName: string,
  status: string,
  message?: string
) => {
  return sendEmail(
    email,
    EMAIL_TEMPLATES.APPLICATION_STATUS_UPDATE.templateId,
    {
      applicant_name: applicantName,
      job_title: jobTitle,
      company_name: companyName,
      status,
      message,
      applications_url: `${process.env.NEXT_PUBLIC_APP_URL}/applications`,
    }
  );
};

export const sendJobAlert = async (
  email: string,
  name: string,
  jobs: Array<{
    title: string;
    company: string;
    location: string;
    url: string;
  }>
) => {
  return sendEmail(email, EMAIL_TEMPLATES.NEW_JOB_ALERT.templateId, {
    name,
    jobs,
    job_alerts_url: `${process.env.NEXT_PUBLIC_APP_URL}/job-alerts`,
  });
};

export const sendPasswordResetEmail = async (
  email: string,
  name: string,
  resetUrl: string
) => {
  return sendEmail(email, EMAIL_TEMPLATES.PASSWORD_RESET.templateId, {
    name,
    reset_url: resetUrl,
  });
};

export const sendInterviewInvitation = async (
  email: string,
  applicantName: string,
  jobTitle: string,
  companyName: string,
  interviewDate: string,
  interviewTime: string,
  location: string,
  instructions?: string
) => {
  return sendEmail(email, EMAIL_TEMPLATES.INTERVIEW_INVITATION.templateId, {
    applicant_name: applicantName,
    job_title: jobTitle,
    company_name: companyName,
    interview_date: interviewDate,
    interview_time: interviewTime,
    location,
    instructions,
  });
};

export { sgMail };
