import { z } from 'zod';
import { UserRole, JobType, ApplicationStatus, ProfileVisibility } from '@/types';

// Auth Schemas
export const loginSchema = z.object({
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address'),
  password: z
    .string()
    .min(1, 'Password is required')
    .min(8, 'Password must be at least 8 characters'),
});

export const registerSchema = z
  .object({
    name: z
      .string()
      .min(1, 'Name is required')
      .min(2, 'Name must be at least 2 characters')
      .max(50, 'Name must be less than 50 characters'),
    email: z
      .string()
      .min(1, 'Email is required')
      .email('Please enter a valid email address'),
    password: z
      .string()
      .min(1, 'Password is required')
      .min(8, 'Password must be at least 8 characters')
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        'Password must contain at least one uppercase letter, one lowercase letter, and one number'
      ),
    confirmPassword: z.string().min(1, 'Please confirm your password'),
    role: z.nativeEnum(UserRole),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  });

// Profile Schemas
export const profileSchema = z.object({
  name: z
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(50, 'Name must be less than 50 characters'),
  email: z.string().email('Please enter a valid email address'),
  mobileNumber: z
    .string()
    .optional()
    .refine(
      val => !val || /^\+?[1-9]\d{1,14}$/.test(val.replace(/\s/g, '')),
      'Please enter a valid phone number'
    ),
  dateOfBirth: z.date().optional(),
  nationality: z.string().optional(),
  address: z.string().optional(),
  skills: z.array(z.string()).default([]),
  desiredJobType: z.string().optional(),
  desiredIndustry: z.string().optional(),
  desiredLocation: z.string().optional(),
  salaryExpectation: z.number().positive().optional(),
  visibility: z.nativeEnum(ProfileVisibility).default(ProfileVisibility.PUBLIC),
});

// Education Schema
export const educationSchema = z.object({
  institution: z.string().min(1, 'Institution is required'),
  degree: z.string().min(1, 'Degree is required'),
  fieldOfStudy: z.string().min(1, 'Field of study is required'),
  startDate: z.date(),
  endDate: z.date().optional(),
  grade: z.string().optional(),
});

// Work Experience Schema
export const workExperienceSchema = z.object({
  company: z.string().min(1, 'Company is required'),
  position: z.string().min(1, 'Position is required'),
  location: z.string().optional(),
  startDate: z.date(),
  endDate: z.date().optional(),
  description: z.string().optional(),
});

// Job Posting Schema
export const jobPostingSchema = z.object({
  title: z
    .string()
    .min(1, 'Job title is required')
    .min(5, 'Job title must be at least 5 characters')
    .max(100, 'Job title must be less than 100 characters'),
  description: z
    .string()
    .min(1, 'Job description is required')
    .min(50, 'Job description must be at least 50 characters')
    .max(5000, 'Job description must be less than 5000 characters'),
  requirements: z
    .array(z.string().min(1))
    .min(1, 'At least one requirement is required'),
  responsibilities: z
    .array(z.string().min(1))
    .min(1, 'At least one responsibility is required'),
  location: z.string().min(1, 'Location is required'),
  salary: z
    .object({
      min: z.number().positive('Minimum salary must be positive'),
      max: z.number().positive('Maximum salary must be positive'),
      currency: z.string().min(1, 'Currency is required'),
    })
    .optional()
    .refine(
      data => !data || data.max >= data.min,
      'Maximum salary must be greater than or equal to minimum salary'
    ),
  jobType: z.nativeEnum(JobType),
  industry: z.string().min(1, 'Industry is required'),
  experienceLevel: z.string().min(1, 'Experience level is required'),
  education: z.string().optional(),
  deadline: z.date().optional(),
});

// Job Application Schema
export const jobApplicationSchema = z.object({
  jobId: z.string().min(1, 'Job ID is required'),
  resumeUrl: z.string().url('Please provide a valid resume URL'),
  coverLetter: z
    .string()
    .optional()
    .refine(
      val => !val || val.length <= 2000,
      'Cover letter must be less than 2000 characters'
    ),
});

// Job Search Schema
export const jobSearchSchema = z.object({
  query: z.string().optional(),
  location: z.string().optional(),
  jobType: z.array(z.nativeEnum(JobType)).optional(),
  industry: z.array(z.string()).optional(),
  experienceLevel: z.array(z.string()).optional(),
  salaryRange: z
    .object({
      min: z.number().positive().optional(),
      max: z.number().positive().optional(),
    })
    .optional()
    .refine(
      data => !data || !data.min || !data.max || data.max >= data.min,
      'Maximum salary must be greater than or equal to minimum salary'
    ),
  datePosted: z.enum(['today', 'week', 'month', 'all']).optional(),
});

// Contact Form Schema
export const contactSchema = z.object({
  name: z
    .string()
    .min(1, 'Name is required')
    .min(2, 'Name must be at least 2 characters'),
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address'),
  subject: z
    .string()
    .min(1, 'Subject is required')
    .max(100, 'Subject must be less than 100 characters'),
  message: z
    .string()
    .min(1, 'Message is required')
    .min(10, 'Message must be at least 10 characters')
    .max(1000, 'Message must be less than 1000 characters'),
});

// Password Change Schema
export const passwordChangeSchema = z
  .object({
    currentPassword: z.string().min(1, 'Current password is required'),
    newPassword: z
      .string()
      .min(1, 'New password is required')
      .min(8, 'Password must be at least 8 characters')
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        'Password must contain at least one uppercase letter, one lowercase letter, and one number'
      ),
    confirmPassword: z.string().min(1, 'Please confirm your new password'),
  })
  .refine(data => data.newPassword === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  })
  .refine(data => data.currentPassword !== data.newPassword, {
    message: 'New password must be different from current password',
    path: ['newPassword'],
  });

// File Upload Schema
export const fileUploadSchema = z.object({
  file: z
    .instanceof(File)
    .refine(file => file.size <= 5 * 1024 * 1024, 'File size must be less than 5MB')
    .refine(
      file => ['pdf', 'doc', 'docx'].includes(file.name.split('.').pop()?.toLowerCase() || ''),
      'File must be a PDF, DOC, or DOCX'
    ),
});

// Image Upload Schema
export const imageUploadSchema = z.object({
  file: z
    .instanceof(File)
    .refine(file => file.size <= 2 * 1024 * 1024, 'Image size must be less than 2MB')
    .refine(
      file => ['jpg', 'jpeg', 'png', 'webp'].includes(file.name.split('.').pop()?.toLowerCase() || ''),
      'Image must be JPG, JPEG, PNG, or WebP'
    ),
});

// Type exports for form data
export type LoginFormData = z.infer<typeof loginSchema>;
export type RegisterFormData = z.infer<typeof registerSchema>;
export type ProfileFormData = z.infer<typeof profileSchema>;
export type EducationFormData = z.infer<typeof educationSchema>;
export type WorkExperienceFormData = z.infer<typeof workExperienceSchema>;
export type JobPostingFormData = z.infer<typeof jobPostingSchema>;
export type JobApplicationFormData = z.infer<typeof jobApplicationSchema>;
export type JobSearchFormData = z.infer<typeof jobSearchSchema>;
export type ContactFormData = z.infer<typeof contactSchema>;
export type PasswordChangeFormData = z.infer<typeof passwordChangeSchema>;
export type FileUploadFormData = z.infer<typeof fileUploadSchema>;
export type ImageUploadFormData = z.infer<typeof imageUploadSchema>;
