import { auth, currentUser } from '@clerk/nextjs/server';
import { prisma } from '@/lib/prisma';
import { Role, UserStatus } from '@prisma/client';

// Get current authenticated user from database
export async function getCurrentUser() {
  try {
    const { userId } = auth();
    
    if (!userId) {
      return null;
    }

    const clerkUser = await currentUser();
    
    if (!clerkUser) {
      return null;
    }

    // Find or create user in database
    let user = await prisma.user.findUnique({
      where: { clerkId: userId },
      include: {
        profile: {
          include: {
            education: true,
            workExperience: true,
            resumes: true,
          },
        },
      },
    });

    // If user doesn't exist in database, create them
    if (!user) {
      const role = determineUserRole(clerkUser.publicMetadata?.role as string);
      
      user = await prisma.user.create({
        data: {
          clerkId: userId,
          email: clerkUser.emailAddresses[0]?.emailAddress || '',
          name: `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim() || 'User',
          role,
          status: UserStatus.ACTIVE,
          emailVerified: clerkUser.emailAddresses[0]?.verification?.status === 'verified',
          mobileNumber: clerkUser.phoneNumbers[0]?.phoneNumber,
          lastLogin: new Date(),
        },
        include: {
          profile: {
            include: {
              education: true,
              workExperience: true,
              resumes: true,
            },
          },
        },
      });
    } else {
      // Update last login
      await prisma.user.update({
        where: { id: user.id },
        data: { lastLogin: new Date() },
      });
    }

    return user;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

// Determine user role from Clerk metadata or default
function determineUserRole(roleFromMetadata?: string): Role {
  switch (roleFromMetadata?.toLowerCase()) {
    case 'employer':
      return Role.EMPLOYER;
    case 'admin':
      return Role.ADMIN;
    case 'job_seeker':
    default:
      return Role.JOB_SEEKER;
  }
}

// Check if user has specific role
export async function hasRole(requiredRole: Role): Promise<boolean> {
  try {
    const user = await getCurrentUser();
    return user?.role === requiredRole;
  } catch (error) {
    console.error('Error checking user role:', error);
    return false;
  }
}

// Check if user has any of the specified roles
export async function hasAnyRole(roles: Role[]): Promise<boolean> {
  try {
    const user = await getCurrentUser();
    return user ? roles.includes(user.role) : false;
  } catch (error) {
    console.error('Error checking user roles:', error);
    return false;
  }
}

// Check if user is employer
export async function isEmployer(): Promise<boolean> {
  return hasRole(Role.EMPLOYER);
}

// Check if user is job seeker
export async function isJobSeeker(): Promise<boolean> {
  return hasRole(Role.JOB_SEEKER);
}

// Check if user is admin
export async function isAdmin(): Promise<boolean> {
  return hasRole(Role.ADMIN);
}

// Get user permissions based on role
export async function getUserPermissions() {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return {
        canPostJobs: false,
        canApplyToJobs: false,
        canViewApplications: false,
        canManageUsers: false,
        canAccessAnalytics: false,
      };
    }

    const basePermissions = {
      canPostJobs: false,
      canApplyToJobs: false,
      canViewApplications: false,
      canManageUsers: false,
      canAccessAnalytics: false,
    };

    switch (user.role) {
      case Role.ADMIN:
        return {
          ...basePermissions,
          canPostJobs: true,
          canApplyToJobs: true,
          canViewApplications: true,
          canManageUsers: true,
          canAccessAnalytics: true,
        };
      case Role.EMPLOYER:
        return {
          ...basePermissions,
          canPostJobs: true,
          canViewApplications: true,
          canAccessAnalytics: true,
        };
      case Role.JOB_SEEKER:
        return {
          ...basePermissions,
          canApplyToJobs: true,
        };
      default:
        return basePermissions;
    }
  } catch (error) {
    console.error('Error getting user permissions:', error);
    return {
      canPostJobs: false,
      canApplyToJobs: false,
      canViewApplications: false,
      canManageUsers: false,
      canAccessAnalytics: false,
    };
  }
}

// Require authentication
export async function requireAuth() {
  const user = await getCurrentUser();
  
  if (!user) {
    throw new Error('Authentication required');
  }
  
  return user;
}

// Require specific role
export async function requireRole(requiredRole: Role) {
  const user = await requireAuth();
  
  if (user.role !== requiredRole) {
    throw new Error(`Role ${requiredRole} required`);
  }
  
  return user;
}

// Require any of the specified roles
export async function requireAnyRole(roles: Role[]) {
  const user = await requireAuth();
  
  if (!roles.includes(user.role)) {
    throw new Error(`One of roles ${roles.join(', ')} required`);
  }
  
  return user;
}

// Check if user owns resource
export async function isResourceOwner(resourceUserId: string): Promise<boolean> {
  try {
    const user = await getCurrentUser();
    return user?.id === resourceUserId;
  } catch (error) {
    console.error('Error checking resource ownership:', error);
    return false;
  }
}

// Require resource ownership or admin role
export async function requireOwnershipOrAdmin(resourceUserId: string) {
  const user = await requireAuth();
  
  const isOwner = user.id === resourceUserId;
  const isAdminUser = user.role === Role.ADMIN;
  
  if (!isOwner && !isAdminUser) {
    throw new Error('Access denied: insufficient permissions');
  }
  
  return user;
}
