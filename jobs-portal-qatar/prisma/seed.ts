import { PrismaClient } from '@prisma/client';
import {
  Role,
  UserStatus,
  JobType,
  WorkType,
  ExperienceLevel,
  JobStatus,
  Visibility,
  VerificationStatus,
} from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create sample companies
  const companies = await Promise.all([
    prisma.company.create({
      data: {
        name: 'Qatar Airways',
        slug: 'qatar-airways',
        description:
          'The national flag carrier of Qatar, operating a hub-and-spoke network.',
        industry: 'Aviation',
        companySize: '10000+',
        foundedYear: 1993,
        website: 'https://qatarairways.com',
        headquarters: 'Doha, Qatar',
        locations: ['Doha', 'Al Rayyan'],
        benefits: [
          'Health Insurance',
          'Flight Benefits',
          'Annual Leave',
          'Training Programs',
        ],
        culture: 'Excellence in service and innovation in aviation.',
        verificationStatus: VerificationStatus.VERIFIED,
        verificationDate: new Date(),
        contactEmail: '<EMAIL>',
        contactPhone: '+974 4449 9999',
        socialLinks: {
          linkedin: 'https://linkedin.com/company/qatar-airways',
          twitter: 'https://twitter.com/qatarairways',
          facebook: 'https://facebook.com/qatarairways',
        },
      },
    }),
    prisma.company.create({
      data: {
        name: 'QatarEnergy',
        slug: 'qatarenergy',
        description:
          'An integrated national oil corporation responsible for all phases of the oil and gas industry in Qatar.',
        industry: 'Oil & Gas',
        companySize: '5000-10000',
        foundedYear: 1974,
        website: 'https://qatarenergy.qa',
        headquarters: 'Doha, Qatar',
        locations: ['Doha', 'Ras Laffan', 'Dukhan'],
        benefits: [
          'Competitive Salary',
          'Health Insurance',
          'Housing Allowance',
          'Education Support',
        ],
        culture:
          'Leading the energy transition while maintaining operational excellence.',
        verificationStatus: VerificationStatus.VERIFIED,
        verificationDate: new Date(),
        contactEmail: '<EMAIL>',
        contactPhone: '+974 4013 2000',
        socialLinks: {
          linkedin: 'https://linkedin.com/company/qatarenergy',
          twitter: 'https://twitter.com/qatarenergy',
        },
      },
    }),
    prisma.company.create({
      data: {
        name: 'Hamad Medical Corporation',
        slug: 'hamad-medical-corporation',
        description:
          'The premier non-profit health care provider in the State of Qatar.',
        industry: 'Healthcare',
        companySize: '1000-5000',
        foundedYear: 1979,
        website: 'https://hamad.qa',
        headquarters: 'Doha, Qatar',
        locations: ['Doha', 'Al Rayyan', 'Al Wakrah'],
        benefits: [
          'Medical Coverage',
          'Professional Development',
          'Research Opportunities',
          'Flexible Hours',
        ],
        culture: 'Committed to providing world-class healthcare services.',
        verificationStatus: VerificationStatus.VERIFIED,
        verificationDate: new Date(),
        contactEmail: '<EMAIL>',
        contactPhone: '+974 4439 4444',
        socialLinks: {
          linkedin: 'https://linkedin.com/company/hamad-medical-corporation',
        },
      },
    }),
  ]);

  console.log(`✅ Created ${companies.length} companies`);

  // Create sample users
  const users = await Promise.all([
    // Job Seekers
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Ahmed Hassan',
        role: Role.JOB_SEEKER,
        status: UserStatus.ACTIVE,
        emailVerified: true,
        mobileNumber: '+974 5555 1234',
        profile: {
          create: {
            firstName: 'Ahmed',
            lastName: 'Hassan',
            dateOfBirth: new Date('1990-05-15'),
            nationality: 'Egyptian',
            city: 'Doha',
            country: 'Qatar',
            phoneNumber: '+974 5555 1234',
            bio: 'Experienced software engineer with expertise in full-stack development.',
            skills: ['JavaScript', 'React', 'Node.js', 'Python', 'MongoDB'],
            languages: ['Arabic', 'English'],
            desiredJobType: 'Full-time',
            desiredIndustry: 'Technology',
            desiredLocation: 'Doha',
            salaryExpectation: {
              min: 15000,
              max: 25000,
              currency: 'QAR',
              period: 'monthly',
            },
            visibility: Visibility.PUBLIC,
            isProfileComplete: true,
            education: {
              create: [
                {
                  institution: 'Cairo University',
                  degree: 'Bachelor of Computer Science',
                  fieldOfStudy: 'Computer Science',
                  startDate: new Date('2008-09-01'),
                  endDate: new Date('2012-06-30'),
                  grade: 'Excellent',
                },
              ],
            },
            workExperience: {
              create: [
                {
                  company: 'Tech Solutions Qatar',
                  position: 'Senior Software Engineer',
                  location: 'Doha, Qatar',
                  startDate: new Date('2020-01-01'),
                  isCurrent: true,
                  description:
                    'Leading development of web applications using modern technologies.',
                  achievements: [
                    'Led team of 5 developers',
                    'Improved system performance by 40%',
                  ],
                  skills: ['React', 'Node.js', 'MongoDB', 'AWS'],
                },
              ],
            },
          },
        },
      },
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Fatima Al-Zahra',
        role: Role.JOB_SEEKER,
        status: UserStatus.ACTIVE,
        emailVerified: true,
        mobileNumber: '+974 5555 5678',
        profile: {
          create: {
            firstName: 'Fatima',
            lastName: 'Al-Zahra',
            dateOfBirth: new Date('1992-08-20'),
            nationality: 'Qatari',
            city: 'Doha',
            country: 'Qatar',
            phoneNumber: '+974 5555 5678',
            bio: 'Marketing professional with experience in digital marketing and brand management.',
            skills: [
              'Digital Marketing',
              'Social Media',
              'Content Creation',
              'Analytics',
              'SEO',
            ],
            languages: ['Arabic', 'English', 'French'],
            desiredJobType: 'Full-time',
            desiredIndustry: 'Marketing',
            desiredLocation: 'Doha',
            salaryExpectation: {
              min: 12000,
              max: 18000,
              currency: 'QAR',
              period: 'monthly',
            },
            visibility: Visibility.PUBLIC,
            isProfileComplete: true,
            education: {
              create: [
                {
                  institution: 'Qatar University',
                  degree: 'Bachelor of Business Administration',
                  fieldOfStudy: 'Marketing',
                  startDate: new Date('2010-09-01'),
                  endDate: new Date('2014-06-30'),
                  grade: 'Very Good',
                },
              ],
            },
            workExperience: {
              create: [
                {
                  company: 'Qatar Marketing Agency',
                  position: 'Marketing Specialist',
                  location: 'Doha, Qatar',
                  startDate: new Date('2018-03-01'),
                  isCurrent: true,
                  description:
                    'Managing digital marketing campaigns and social media presence.',
                  achievements: [
                    'Increased social media engagement by 60%',
                    'Managed campaigns worth 500K QAR',
                  ],
                  skills: [
                    'Facebook Ads',
                    'Google Analytics',
                    'Content Strategy',
                  ],
                },
              ],
            },
          },
        },
      },
    }),
    // Employers
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Qatar Airways HR',
        role: Role.EMPLOYER,
        status: UserStatus.ACTIVE,
        emailVerified: true,
        mobileNumber: '+974 4449 9999',
      },
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'QatarEnergy Recruitment',
        role: Role.EMPLOYER,
        status: UserStatus.ACTIVE,
        emailVerified: true,
        mobileNumber: '+974 4013 2000',
      },
    }),
  ]);

  console.log(`✅ Created ${users.length} users`);

  // Create sample job posts
  const jobPosts = await Promise.all([
    prisma.jobPost.create({
      data: {
        employerId: users[2].id, // Qatar Airways HR
        companyId: companies[0].id, // Qatar Airways
        title: 'Senior Software Engineer',
        slug: 'senior-software-engineer-qatar-airways',
        description:
          'We are looking for a Senior Software Engineer to join our IT team and help build next-generation aviation systems.',
        requirements: [
          '5+ years of software development experience',
          'Proficiency in Java, Python, or C#',
          'Experience with cloud platforms (AWS, Azure)',
          'Strong problem-solving skills',
          "Bachelor's degree in Computer Science or related field",
        ],
        responsibilities: [
          'Design and develop scalable software solutions',
          'Collaborate with cross-functional teams',
          'Mentor junior developers',
          'Participate in code reviews and technical discussions',
          'Ensure code quality and best practices',
        ],
        benefits: [
          'Competitive salary package',
          'Flight benefits for employee and family',
          'Health insurance',
          'Annual leave',
          'Professional development opportunities',
        ],
        location: 'Doha, Qatar',
        salary: {
          min: 20000,
          max: 30000,
          currency: 'QAR',
          period: 'monthly',
        },
        jobType: JobType.FULL_TIME,
        workType: WorkType.ON_SITE,
        industry: 'Technology',
        experienceLevel: ExperienceLevel.SENIOR_LEVEL,
        educationLevel: "Bachelor's Degree",
        deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        applicationEmail: '<EMAIL>',
        status: JobStatus.ACTIVE,
        featured: true,
        publishedAt: new Date(),
        expiresAt: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days from now
      },
    }),
    prisma.jobPost.create({
      data: {
        employerId: users[3].id, // QatarEnergy Recruitment
        companyId: companies[1].id, // QatarEnergy
        title: 'Petroleum Engineer',
        slug: 'petroleum-engineer-qatarenergy',
        description:
          'Join our engineering team to work on cutting-edge oil and gas projects in Qatar.',
        requirements: [
          "Bachelor's degree in Petroleum Engineering",
          '3+ years of experience in oil and gas industry',
          'Knowledge of reservoir engineering principles',
          'Proficiency in petroleum engineering software',
          'Strong analytical and problem-solving skills',
        ],
        responsibilities: [
          'Design and optimize drilling operations',
          'Analyze reservoir performance',
          'Collaborate with multidisciplinary teams',
          'Ensure compliance with safety regulations',
          'Prepare technical reports and presentations',
        ],
        benefits: [
          'Competitive salary and benefits',
          'Housing allowance',
          'Transportation allowance',
          'Health insurance for family',
          'Annual bonus',
        ],
        location: 'Ras Laffan, Qatar',
        salary: {
          min: 25000,
          max: 35000,
          currency: 'QAR',
          period: 'monthly',
        },
        jobType: JobType.FULL_TIME,
        workType: WorkType.ON_SITE,
        industry: 'Oil & Gas',
        experienceLevel: ExperienceLevel.MID_LEVEL,
        educationLevel: "Bachelor's Degree",
        deadline: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000), // 45 days from now
        applicationEmail: '<EMAIL>',
        status: JobStatus.ACTIVE,
        featured: true,
        publishedAt: new Date(),
        expiresAt: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days from now
      },
    }),
  ]);

  console.log(`✅ Created ${jobPosts.length} job posts`);

  console.log('🎉 Database seeding completed successfully!');
}

main()
  .catch(e => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
