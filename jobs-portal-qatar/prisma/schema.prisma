// This is your Prisma schema file
datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

// User model for both job seekers and employers
model User {
  id             String    @id @default(auto()) @map("_id") @db.ObjectId
  clerkId        String?   // Clerk user ID
  email          String    @unique
  password       String?   // Optional for Clerk users
  mobileNumber   String?   @unique
  name           String
  role           Role      @default(JOB_SEEKER)
  status         UserStatus @default(ACTIVE)
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  lastLogin      DateTime?
  emailVerified  <PERSON><PERSON><PERSON>   @default(false)

  // Relations
  profile        Profile?
  jobPosts       JobPost[] @relation("EmployerPosts")
  applications   JobApplication[] @relation("UserApplications")
  savedJobs      SavedJob[] @relation("UserSavedJobs")
  notifications  Notification[] @relation("UserNotifications")
  jobAlerts      JobAlert[] @relation("UserJobAlerts")

  @@index([role, status])
  @@unique([clerkId])
}

enum Role {
  JOB_SEEKER
  EMPLOYER
  ADMIN
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  PENDING_VERIFICATION
}

// Profile model for additional user information
model Profile {
  id               String    @id @default(auto()) @map("_id") @db.ObjectId
  userId           String    @unique @db.ObjectId
  user             User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Personal Information
  firstName        String?
  lastName         String?
  dateOfBirth      DateTime?
  nationality      String?
  address          String?
  city             String?
  country          String?
  phoneNumber      String?
  profilePicture   String?
  bio              String?

  // Professional Information
  skills           String[]
  languages        String[]
  desiredJobType   String?
  desiredIndustry  String?
  desiredLocation  String?
  salaryExpectation SalaryExpectation?
  availabilityDate DateTime?

  // Settings
  visibility       Visibility @default(PUBLIC)
  isProfileComplete Boolean @default(false)

  // Relations
  education        Education[]
  workExperience   WorkExperience[]
  resumes          Resume[]

  createdAt        DateTime   @default(now())
  updatedAt        DateTime   @updatedAt

  @@index([city, country])
  @@index([desiredIndustry, desiredJobType])
}

type SalaryExpectation {
  min      Float
  max      Float
  currency String
  period   String // "monthly", "yearly"
}

enum Visibility {
  PUBLIC
  PRIVATE
  PARTIAL
}

// Resume model
model Resume {
  id            String   @id @default(auto()) @map("_id") @db.ObjectId
  profileId     String   @db.ObjectId
  profile       Profile  @relation(fields: [profileId], references: [id], onDelete: Cascade)
  fileName      String
  originalName  String
  fileUrl       String
  fileSize      Int
  mimeType      String
  isPrimary     Boolean  @default(false)
  parsedContent String?  // AI-parsed content for search
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@index([profileId, isPrimary])
  @@index([createdAt])
}

// Education model for user profiles
model Education {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  profileId   String   @db.ObjectId
  profile     Profile  @relation(fields: [profileId], references: [id], onDelete: Cascade)
  institution String
  degree      String
  fieldOfStudy String
  startDate   DateTime
  endDate     DateTime?
  grade       String?
  description String?
  isCurrently Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([profileId, startDate])
}

// Work Experience model for user profiles
model WorkExperience {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  profileId   String   @db.ObjectId
  profile     Profile  @relation(fields: [profileId], references: [id], onDelete: Cascade)
  company     String
  position    String
  location    String?
  startDate   DateTime
  endDate     DateTime?
  isCurrent   Boolean  @default(false)
  description String?
  achievements String[]
  skills      String[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([profileId, startDate])
}

// Company model
model Company {
  id                  String   @id @default(auto()) @map("_id") @db.ObjectId
  name                String
  slug                String   @unique
  description         String
  industry            String
  companySize         String?
  foundedYear         Int?
  website             String?
  logo                String?
  coverImage          String?
  headquarters        String?
  locations           String[]
  socialLinks         SocialLinks?
  benefits            String[]
  culture             String?
  verificationStatus  VerificationStatus @default(PENDING)
  verificationDate    DateTime?
  contactEmail        String
  contactPhone        String?

  // Relations
  jobPosts            JobPost[] @relation("CompanyJobs")

  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  @@index([name])
  @@index([industry, companySize])
  @@index([verificationStatus])
}

type SocialLinks {
  linkedin   String?
  twitter    String?
  facebook   String?
  instagram  String?
}

enum VerificationStatus {
  PENDING
  VERIFIED
  REJECTED
}

// Job Post model
model JobPost {
  id              String    @id @default(auto()) @map("_id") @db.ObjectId
  employerId      String    @db.ObjectId
  employer        User      @relation("EmployerPosts", fields: [employerId], references: [id])
  companyId       String?   @db.ObjectId
  company         Company?  @relation("CompanyJobs", fields: [companyId], references: [id])

  title           String
  slug            String
  description     String
  requirements    String[]
  responsibilities String[]
  benefits        String[]

  // Job Details
  location        String
  salary          Salary?
  jobType         JobType
  workType        WorkType  @default(ON_SITE)
  industry        String
  experienceLevel ExperienceLevel
  educationLevel  String?

  // Application Settings
  deadline        DateTime?
  applicationEmail String?
  applicationUrl  String?

  // Status and Metadata
  status          JobStatus @default(DRAFT)
  featured        Boolean   @default(false)
  urgent          Boolean   @default(false)
  views           Int       @default(0)

  // Relations
  applications    JobApplication[] @relation("JobApplications")
  savedBy         SavedJob[] @relation("SavedJobs")

  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  publishedAt     DateTime?
  expiresAt       DateTime?

  @@index([employerId, status])
  @@index([status, publishedAt])
  @@index([industry, jobType, location])
  @@index([featured, urgent, publishedAt])
  @@index([expiresAt])
}

type Salary {
  min      Float
  max      Float
  currency String
  period   String // "monthly", "yearly"
}

enum JobType {
  FULL_TIME
  PART_TIME
  CONTRACT
  FREELANCE
  INTERNSHIP
  TEMPORARY
}

enum WorkType {
  ON_SITE
  REMOTE
  HYBRID
}

enum ExperienceLevel {
  ENTRY_LEVEL
  MID_LEVEL
  SENIOR_LEVEL
  EXECUTIVE
}

enum JobStatus {
  DRAFT
  ACTIVE
  PAUSED
  CLOSED
  EXPIRED
}

// Job Application model
model JobApplication {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  jobId       String    @db.ObjectId
  job         JobPost   @relation("JobApplications", fields: [jobId], references: [id], onDelete: Cascade)
  applicantId String    @db.ObjectId
  applicant   User      @relation("UserApplications", fields: [applicantId], references: [id], onDelete: Cascade)

  // Application Data
  resumeUrl   String
  coverLetter String?
  portfolioUrl String?
  expectedSalary Float?
  availableFrom DateTime?

  // Status and Tracking
  status      ApplicationStatus @default(PENDING)
  stage       String?           // Interview stage, etc.
  rating      Int?              // 1-5 rating by employer
  notes       String?           // Employer notes

  // Timestamps
  appliedAt   DateTime  @default(now())
  reviewedAt  DateTime?
  respondedAt DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@unique([jobId, applicantId])
  @@index([applicantId, appliedAt])
  @@index([jobId, status])
  @@index([status, appliedAt])
}

enum ApplicationStatus {
  PENDING
  REVIEWED
  SHORTLISTED
  INTERVIEW_SCHEDULED
  INTERVIEWED
  OFFER_MADE
  ACCEPTED
  REJECTED
  WITHDRAWN
}

// Saved Jobs model
model SavedJob {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  jobId     String   @db.ObjectId
  job       JobPost  @relation("SavedJobs", fields: [jobId], references: [id], onDelete: Cascade)
  userId    String   @db.ObjectId
  user      User     @relation("UserSavedJobs", fields: [userId], references: [id], onDelete: Cascade)
  notes     String?  // Personal notes about the job
  createdAt DateTime @default(now())

  @@unique([jobId, userId])
  @@index([userId, createdAt])
}

// Notification model
model Notification {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  userId    String   @db.ObjectId
  user      User     @relation("UserNotifications", fields: [userId], references: [id], onDelete: Cascade)

  type      NotificationType
  title     String
  message   String
  data      Json?    // Additional data for the notification

  isRead    Boolean  @default(false)
  readAt    DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId, isRead, createdAt])
  @@index([type, createdAt])
}

enum NotificationType {
  APPLICATION_STATUS
  NEW_JOB_MATCH
  JOB_ALERT
  MESSAGE
  SYSTEM
  PROMOTION
}

// Job Alert model
model JobAlert {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  userId      String   @db.ObjectId
  user        User     @relation("UserJobAlerts", fields: [userId], references: [id], onDelete: Cascade)

  name        String
  keywords    String[]
  location    String?
  jobType     JobType[]
  workType    WorkType[]
  industry    String[]
  salaryMin   Float?
  salaryMax   Float?

  isActive    Boolean  @default(true)
  frequency   AlertFrequency @default(DAILY)
  lastSent    DateTime?

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([userId, isActive])
  @@index([isActive, frequency])
}

enum AlertFrequency {
  IMMEDIATE
  DAILY
  WEEKLY
}
