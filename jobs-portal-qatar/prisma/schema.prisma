// This is your Prisma schema file
datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

// User model for both job seekers and employers
model User {
  id             String    @id @default(auto()) @map("_id") @db.ObjectId
  email          String    @unique
  password       String?
  mobileNumber   String?   @unique
  name           String
  role           Role      @default(JOB_SEEKER)
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  emailVerified  Boolean   @default(false)
  profile        Profile?
  jobPosts       JobPost[] @relation("EmployerPosts")
  applications   JobApplication[] @relation("UserApplications")
  savedJobs      SavedJob[] @relation("UserSavedJobs")
}

enum Role {
  JOB_SEEKER
  EMPLOYER
  ADMIN
}

// Profile model for additional user information
model Profile {
  id               String    @id @default(auto()) @map("_id") @db.ObjectId
  userId           String    @unique @db.ObjectId
  user             User      @relation(fields: [userId], references: [id])
  dateOfBirth      DateTime?
  nationality      String?
  address          String?
  skills           String[]
  education        Education[]
  workExperience   WorkExperience[]
  resumeUrl        String?
  profilePicture   String?
  desiredJobType   String?
  desiredIndustry  String?
  desiredLocation  String?
  salaryExpectation Float?
  visibility       Visibility @default(PUBLIC)
  createdAt        DateTime   @default(now())
  updatedAt        DateTime   @updatedAt
}

enum Visibility {
  PUBLIC
  PRIVATE
  PARTIAL
}

// Education model for user profiles
model Education {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  profileId   String   @db.ObjectId
  profile     Profile  @relation(fields: [profileId], references: [id])
  institution String
  degree      String
  fieldOfStudy String
  startDate   DateTime
  endDate     DateTime?
  grade       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// Work Experience model for user profiles
model WorkExperience {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  profileId   String   @db.ObjectId
  profile     Profile  @relation(fields: [profileId], references: [id])
  company     String
  position    String
  location    String?
  startDate   DateTime
  endDate     DateTime?
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// Job Post model
model JobPost {
  id              String    @id @default(auto()) @map("_id") @db.ObjectId
  employerId      String    @db.ObjectId
  employer        User      @relation("EmployerPosts", fields: [employerId], references: [id])
  title           String
  description     String
  requirements    String[]
  responsibilities String[]
  location        String
  salary          Salary?
  jobType         JobType
  industry        String
  experienceLevel String
  education       String?
  deadline        DateTime?
  status          JobStatus @default(ACTIVE)
  applications    JobApplication[] @relation("JobApplications")
  savedBy         SavedJob[] @relation("SavedJobs")
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
}

type Salary {
  min     Float
  max     Float
  currency String
}

enum JobType {
  FULL_TIME
  PART_TIME
  CONTRACT
  FREELANCE
  INTERNSHIP
}

enum JobStatus {
  ACTIVE
  CLOSED
  DRAFT
}

// Job Application model
model JobApplication {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  jobId       String    @db.ObjectId
  job         JobPost   @relation("JobApplications", fields: [jobId], references: [id])
  applicantId String    @db.ObjectId
  applicant   User      @relation("UserApplications", fields: [applicantId], references: [id])
  resumeUrl   String
  coverLetter String?
  status      ApplicationStatus @default(PENDING)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

enum ApplicationStatus {
  PENDING
  REVIEWED
  SHORTLISTED
  REJECTED
  ACCEPTED
}

// Saved Jobs model
model SavedJob {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  jobId     String   @db.ObjectId
  job       JobPost  @relation("SavedJobs", fields: [jobId], references: [id])
  userId    String   @db.ObjectId
  user      User     @relation("UserSavedJobs", fields: [userId], references: [id])
  createdAt DateTime @default(now())

  @@unique([jobId, userId])
}
