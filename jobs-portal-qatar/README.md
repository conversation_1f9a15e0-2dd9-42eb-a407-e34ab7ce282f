# Jobs Portal Qatar

A modern, full-stack job portal application built with Next.js 15, designed specifically for the Qatar job market. This platform connects job seekers with employers, providing a comprehensive solution for recruitment and career development.

## 🚀 Features

- **Modern Tech Stack**: Built with Next.js 15, TypeScript, Tailwind CSS, and Prisma
- **Authentication**: Secure authentication with Clerk
- **Job Management**: Complete job posting, searching, and application system
- **User Profiles**: Comprehensive profiles for both job seekers and employers
- **Real-time Features**: Live notifications and messaging
- **File Management**: Resume and document upload with ImageKit
- **Payment Integration**: Stripe integration for premium features
- **Responsive Design**: Mobile-first, responsive design
- **Internationalization**: Support for Arabic and English

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: MongoDB
- **Authentication**: Clerk
- **File Storage**: ImageKit
- **Payments**: Stripe
- **Notifications**: Firebase Cloud Messaging
- **Search**: Algolia
- **Testing**: Jest, React Testing Library
- **CI/CD**: GitHub Actions

## 📋 Prerequisites

- Node.js 18+
- pnpm (recommended) or npm
- MongoDB Atlas account
- Clerk account for authentication

## 🚀 Getting Started

1. **Clone the repository**

   ```bash
   git clone https://github.com/your-username/jobs-portal-qatar.git
   cd jobs-portal-qatar
   ```

2. **Install dependencies**

   ```bash
   pnpm install
   ```

3. **Set up environment variables**

   ```bash
   cp .env.example .env.local
   ```

   Fill in your environment variables in `.env.local`

4. **Set up the database**

   ```bash
   pnpm db:generate
   pnpm db:push
   ```

5. **Run the development server**

   ```bash
   pnpm dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📝 Available Scripts

- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm start` - Start production server
- `pnpm lint` - Run ESLint
- `pnpm lint:fix` - Fix ESLint errors
- `pnpm type-check` - Run TypeScript type checking
- `pnpm test` - Run tests
- `pnpm test:watch` - Run tests in watch mode
- `pnpm test:coverage` - Run tests with coverage
- `pnpm db:generate` - Generate Prisma client
- `pnpm db:push` - Push database schema
- `pnpm db:studio` - Open Prisma Studio

## 🏗️ Project Structure

```
jobs-portal-qatar/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Auth route group
│   ├── (dashboard)/       # Dashboard route group
│   ├── api/               # API routes
│   └── globals.css        # Global styles
├── components/            # Reusable components
│   ├── common/           # Common UI components
│   ├── forms/            # Form components
│   ├── layout/           # Layout components
│   └── modules/          # Feature-specific components
├── lib/                  # Utility functions
│   ├── auth/            # Auth configuration
│   ├── utils/           # Helper functions
│   └── validations/     # Zod schemas
├── hooks/                # Custom React hooks
├── providers/            # Context providers
├── types/                # TypeScript definitions
├── constants/            # App constants
└── public/               # Static assets
```

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Run tests with coverage
pnpm test:coverage
```

## 🚀 Deployment

The application is configured for deployment on Vercel:

1. **Connect your repository** to Vercel
2. **Set environment variables** in Vercel dashboard
3. **Deploy** - Vercel will automatically build and deploy

For other platforms, build the application:

```bash
pnpm build
pnpm start
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) for the amazing framework
- [Tailwind CSS](https://tailwindcss.com/) for the utility-first CSS framework
- [Clerk](https://clerk.dev/) for authentication
- [Prisma](https://prisma.io/) for the database toolkit

## 📞 Support

For support, email <EMAIL> or join our Slack channel.
