import { SignIn } from '@clerk/nextjs';
import Link from 'next/link';
import { APP_NAME } from '@/constants';

export default function SignInPage() {
  return (
    <div className="flex min-h-screen">
      {/* Left side - Branding */}
      <div className="relative hidden bg-primary-600 lg:flex lg:w-1/2">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-600 to-primary-800" />
        <div className="relative z-10 flex flex-col justify-center px-12 text-white">
          <div className="max-w-md">
            <h1 className="mb-6 text-4xl font-bold">
              Welcome back to {APP_NAME}
            </h1>
            <p className="mb-8 text-xl text-primary-100">
              Continue your journey to find the perfect job or hire the best
              talent in Qatar.
            </p>
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary-400">
                  <svg
                    className="h-4 w-4"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <span>Access thousands of job opportunities</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary-400">
                  <svg
                    className="h-4 w-4"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <span>Connect with top employers</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary-400">
                  <svg
                    className="h-4 w-4"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <span>Manage your applications</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right side - Sign In Form */}
      <div className="flex flex-1 flex-col justify-center px-6 py-12 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="mb-8 text-center">
            <Link href="/" className="text-2xl font-bold text-primary-600">
              {APP_NAME}
            </Link>
            <h2 className="mt-6 text-3xl font-bold tracking-tight text-neutral-900">
              Sign in to your account
            </h2>
            <p className="mt-2 text-sm text-neutral-600">
              Don&apos;t have an account?{' '}
              <Link
                href="/sign-up"
                className="font-medium text-primary-600 hover:text-primary-500"
              >
                Sign up for free
              </Link>
            </p>
          </div>

          <div className="rounded-xl border border-neutral-200 bg-white px-6 py-8 shadow-soft">
            <SignIn
              appearance={{
                elements: {
                  formButtonPrimary:
                    'bg-primary-600 hover:bg-primary-700 text-sm normal-case',
                  card: 'shadow-none',
                  headerTitle: 'hidden',
                  headerSubtitle: 'hidden',
                  socialButtonsBlockButton:
                    'border-neutral-300 hover:bg-neutral-50 text-neutral-700',
                  socialButtonsBlockButtonText: 'font-medium',
                  formFieldInput:
                    'border-neutral-300 focus:border-primary-500 focus:ring-primary-500',
                  footerActionLink: 'text-primary-600 hover:text-primary-500',
                },
              }}
              redirectUrl="/dashboard"
              signUpUrl="/sign-up"
            />
          </div>

          <div className="mt-8 text-center">
            <p className="text-xs text-neutral-500">
              By signing in, you agree to our{' '}
              <Link
                href="/terms"
                className="text-primary-600 hover:text-primary-500"
              >
                Terms of Service
              </Link>{' '}
              and{' '}
              <Link
                href="/privacy"
                className="text-primary-600 hover:text-primary-500"
              >
                Privacy Policy
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
