import { headers } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { Webhook } from 'svix';
import { prisma } from '@/lib/prisma';
import { Role, UserStatus } from '@prisma/client';

const webhookSecret = process.env.CLERK_WEBHOOK_SECRET || '';

if (!webhookSecret) {
  throw new Error('Please add CLERK_WEBHOOK_SECRET to your environment variables');
}

export async function POST(req: NextRequest) {
  try {
    // Get the headers
    const headerPayload = await headers();
    const svix_id = headerPayload.get('svix-id');
    const svix_timestamp = headerPayload.get('svix-timestamp');
    const svix_signature = headerPayload.get('svix-signature');

    // If there are no headers, error out
    if (!svix_id || !svix_timestamp || !svix_signature) {
      return new NextResponse('Error occurred -- no svix headers', {
        status: 400,
      });
    }

    // Get the body
    const payload = await req.json();
    const body = JSON.stringify(payload);

    // Create a new Svix instance with your secret.
    const wh = new Webhook(webhookSecret);

    let evt: any;

    // Verify the payload with the headers
    try {
      evt = wh.verify(body, {
        'svix-id': svix_id,
        'svix-timestamp': svix_timestamp,
        'svix-signature': svix_signature,
      }) as any;
    } catch (err) {
      console.error('Error verifying webhook:', err);
      return new NextResponse('Error occurred', {
        status: 400,
      });
    }

    // Handle the webhook
    const eventType = evt.type;
    const { id, email_addresses, first_name, last_name, phone_numbers, public_metadata } = evt.data;

    console.log(`Webhook received: ${eventType}`);

    switch (eventType) {
      case 'user.created':
        await handleUserCreated({
          clerkId: id,
          email: email_addresses[0]?.email_address || '',
          firstName: first_name || '',
          lastName: last_name || '',
          phoneNumber: phone_numbers[0]?.phone_number,
          role: public_metadata?.role as string,
        });
        break;

      case 'user.updated':
        await handleUserUpdated({
          clerkId: id,
          email: email_addresses[0]?.email_address || '',
          firstName: first_name || '',
          lastName: last_name || '',
          phoneNumber: phone_numbers[0]?.phone_number,
          role: public_metadata?.role as string,
        });
        break;

      case 'user.deleted':
        await handleUserDeleted(id);
        break;

      default:
        console.log(`Unhandled webhook event: ${eventType}`);
    }

    return new NextResponse('Webhook processed successfully', { status: 200 });
  } catch (error) {
    console.error('Error processing webhook:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}

async function handleUserCreated(userData: {
  clerkId: string;
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  role?: string;
}) {
  try {
    const role = determineUserRole(userData.role);
    const name = `${userData.firstName} ${userData.lastName}`.trim() || 'User';

    const user = await prisma.user.create({
      data: {
        clerkId: userData.clerkId,
        email: userData.email,
        name,
        role,
        status: UserStatus.ACTIVE,
        emailVerified: true, // Clerk handles email verification
        mobileNumber: userData.phoneNumber || null,
        profile: {
          create: {
            firstName: userData.firstName,
            lastName: userData.lastName,
            phoneNumber: userData.phoneNumber || null,
            visibility: 'PUBLIC',
            isProfileComplete: false,
          },
        },
      },
    });

    console.log(`User created in database: ${user.id}`);
  } catch (error) {
    console.error('Error creating user in database:', error);
    throw error;
  }
}

async function handleUserUpdated(userData: {
  clerkId: string;
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  role?: string;
}) {
  try {
    const role = determineUserRole(userData.role);
    const name = `${userData.firstName} ${userData.lastName}`.trim() || 'User';

    const user = await prisma.user.findUnique({
      where: { clerkId: userData.clerkId },
      include: { profile: true },
    });

    if (!user) {
      // If user doesn't exist, create them
      await handleUserCreated(userData);
      return;
    }

    // Update user
    await prisma.user.update({
      where: { clerkId: userData.clerkId },
      data: {
        email: userData.email,
        name,
        role,
        mobileNumber: userData.phoneNumber || null,
      },
    });

    // Update profile if it exists
    if (user.profile) {
      await prisma.profile.update({
        where: { userId: user.id },
        data: {
          firstName: userData.firstName,
          lastName: userData.lastName,
          phoneNumber: userData.phoneNumber || null,
        },
      });
    }

    console.log(`User updated in database: ${user.id}`);
  } catch (error) {
    console.error('Error updating user in database:', error);
    throw error;
  }
}

async function handleUserDeleted(clerkId: string) {
  try {
    const user = await prisma.user.findUnique({
      where: { clerkId },
    });

    if (!user) {
      console.log(`User with Clerk ID ${clerkId} not found in database`);
      return;
    }

    // Soft delete by updating status
    await prisma.user.update({
      where: { clerkId },
      data: {
        status: UserStatus.INACTIVE,
      },
    });

    console.log(`User soft deleted in database: ${user.id}`);
  } catch (error) {
    console.error('Error deleting user in database:', error);
    throw error;
  }
}

function determineUserRole(roleFromMetadata?: string): Role {
  switch (roleFromMetadata?.toLowerCase()) {
    case 'employer':
      return Role.EMPLOYER;
    case 'admin':
      return Role.ADMIN;
    case 'job_seeker':
    default:
      return Role.JOB_SEEKER;
  }
}
