import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser, requireAuth } from '@/lib/auth/utils';
import { prisma } from '@/lib/prisma';
import { profileSchema } from '@/lib/validations';
import { z } from 'zod';

// GET /api/auth/profile - Get current user profile
export async function GET() {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        status: user.status,
        emailVerified: user.emailVerified,
        mobileNumber: user.mobileNumber,
        createdAt: user.createdAt,
        lastLogin: user.lastLogin,
        profile: user.profile,
      },
    });
  } catch (error) {
    console.error('Error getting user profile:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PATCH /api/auth/profile - Update current user profile
export async function PATCH(req: NextRequest) {
  try {
    const user = await requireAuth();
    const body = await req.json();

    // Validate the request body
    const validationResult = profileSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation failed',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // Update user basic info
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: {
        name: data.name,
        email: data.email,
        mobileNumber: data.mobileNumber,
      },
    });

    // Update or create profile
    const profileData = {
      firstName: data.name.split(' ')[0] || '',
      lastName: data.name.split(' ').slice(1).join(' ') || '',
      dateOfBirth: data.dateOfBirth,
      nationality: data.nationality,
      address: data.address,
      phoneNumber: data.mobileNumber,
      skills: data.skills,
      desiredJobType: data.desiredJobType,
      desiredIndustry: data.desiredIndustry,
      desiredLocation: data.desiredLocation,
      salaryExpectation: data.salaryExpectation ? {
        min: data.salaryExpectation,
        max: data.salaryExpectation * 1.5, // Default max to 1.5x min
        currency: 'QAR',
        period: 'monthly',
      } : undefined,
      visibility: data.visibility,
    };

    let profile;
    if (user.profile) {
      // Update existing profile
      profile = await prisma.profile.update({
        where: { userId: user.id },
        data: profileData,
        include: {
          education: true,
          workExperience: true,
          resumes: true,
        },
      });
    } else {
      // Create new profile
      profile = await prisma.profile.create({
        data: {
          ...profileData,
          userId: user.id,
          isProfileComplete: checkProfileCompleteness(profileData),
        },
        include: {
          education: true,
          workExperience: true,
          resumes: true,
        },
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        ...updatedUser,
        profile,
      },
      message: 'Profile updated successfully',
    });
  } catch (error) {
    console.error('Error updating user profile:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation failed',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper function to check profile completeness
function checkProfileCompleteness(profileData: any): boolean {
  const requiredFields = [
    'firstName',
    'lastName',
    'phoneNumber',
    'skills',
    'desiredJobType',
    'desiredIndustry',
    'desiredLocation',
  ];

  return requiredFields.every(field => {
    const value = profileData[field];
    if (Array.isArray(value)) {
      return value.length > 0;
    }
    return value && value.toString().trim().length > 0;
  });
}
