import { NextResponse } from "next/server";
import { z } from "zod";
import { hash } from "bcryptjs";
import { prisma } from "@/lib/prisma";

const jobSeekerSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
      "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"
    ),
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  mobileNumber: z.string().regex(/^\+?[1-9]\d{1,14}$/, "Invalid mobile number"),
});

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const validatedData = jobSeekerSchema.parse(body);

    // Check if user already exists
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { email: validatedData.email },
          { mobileNumber: validatedData.mobileNumber },
        ],
      },
    });

    if (existingUser) {
      return NextResponse.json(
        {
          message: "A user with this email or mobile number already exists",
        },
        { status: 400 }
      );
    }

    // Hash password
    const hashedPassword = await hash(validatedData.password, 12);

    // Create user and job seeker profile in a transaction
    const newUser = await prisma.$transaction(async (tx) => {
      // Create user
      const createdUser = await tx.user.create({
        data: {
          email: validatedData.email,
          mobileNumber: validatedData.mobileNumber,
          password: hashedPassword,
          firstName: validatedData.firstName,
          lastName: validatedData.lastName,
          role: "JOB_SEEKER",
        },
      });

      // Create job seeker profile
      await tx.jobSeeker.create({
        data: {
          userId: createdUser.id,
          firstName: validatedData.firstName,
          lastName: validatedData.lastName,
          currentLocation: "", // Can be updated later
          preferredLocations: [],
          profileVisibility: "PUBLIC",
        },
      });

      return createdUser;
    });

    return NextResponse.json(
      {
        message: "User registered successfully",
        userId: newUser.id,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Registration error:", error);
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          message: "Invalid input data",
          errors: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        message: "An error occurred during registration",
      },
      { status: 500 }
    );
  }
}
