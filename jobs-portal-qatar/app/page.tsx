import {
  MagnifyingGlassIcon,
  BriefcaseIcon,
  BuildingOfficeIcon,
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import { Button } from '@/components/common';
import { ROUTES, INDUSTRIES } from '@/constants';

const jobCategories = INDUSTRIES.slice(0, 6).map(industry => ({
  name: industry,
  count: Math.floor(Math.random() * 1000) + 500,
}));

const featuredCompanies = [
  { name: 'Tech Corp', logo: '/companies/tech-corp.png', jobCount: 15 },
  { name: 'Health Plus', logo: '/companies/health-plus.png', jobCount: 8 },
  { name: 'Finance Pro', logo: '/companies/finance-pro.png', jobCount: 12 },
  { name: 'Edu World', logo: '/companies/edu-world.png', jobCount: 6 },
];

export default function Home() {
  return (
    <main>
      {/* Hero Section */}
      <div className="bg-primary-50">
        <div className="mx-auto max-w-7xl px-4 py-20 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold tracking-tight text-neutral-900 sm:text-5xl md:text-6xl">
              <span className="block">Find Your Dream Job in</span>
              <span className="block text-primary-600">Qatar</span>
            </h1>
            <p className="mx-auto mt-3 max-w-md text-base text-neutral-600 sm:text-lg md:mt-5 md:max-w-3xl md:text-xl">
              Discover thousands of job opportunities with top companies. Your
              next career move starts here.
            </p>
          </div>

          {/* Search Bar */}
          <div className="mt-10">
            <div className="mx-auto max-w-xl">
              <div className="flex rounded-lg shadow-soft">
                <div className="relative flex-grow">
                  <input
                    type="text"
                    className="block w-full rounded-l-lg border-0 py-3 pl-4 pr-10 text-neutral-900 ring-1 ring-inset ring-neutral-300 placeholder:text-neutral-500 focus:ring-2 focus:ring-inset focus:ring-primary-500"
                    placeholder="Job title, keywords, or company"
                  />
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                    <MagnifyingGlassIcon
                      className="h-5 w-5 text-neutral-400"
                      aria-hidden="true"
                    />
                  </div>
                </div>
                <Button className="rounded-l-none">Search</Button>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="mt-8 flex items-center justify-center gap-x-6">
            <Link href={ROUTES.JOBS.SEARCH}>
              <Button variant="outline">Browse All Jobs</Button>
            </Link>
            <Link href={ROUTES.JOBS.POST}>
              <Button variant="ghost">Post a Job</Button>
            </Link>
          </div>
        </div>
      </div>

      {/* Job Categories */}
      <div className="bg-white py-16">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900">
              Popular Job Categories
            </h2>
            <p className="mt-4 text-lg text-gray-500">
              Browse jobs by category and find the perfect role for you
            </p>
          </div>
          <div className="mt-12 grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {jobCategories.map(category => (
              <Link
                key={category.name}
                href={`/jobs/categories/${category.name.toLowerCase()}`}
                className="group relative rounded-lg border border-gray-300 p-6 hover:border-indigo-600 hover:ring-1 hover:ring-indigo-600"
              >
                <h3 className="text-lg font-semibold text-gray-900 group-hover:text-indigo-600">
                  {category.name}
                </h3>
                <p className="mt-2 text-sm text-gray-500">
                  {category.count} open positions
                </p>
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* Featured Companies */}
      <div className="bg-gray-50 py-16">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900">
              Featured Companies
            </h2>
            <p className="mt-4 text-lg text-gray-500">
              Top companies hiring on our platform
            </p>
          </div>
          <div className="mt-12 grid gap-8 sm:grid-cols-2 lg:grid-cols-4">
            {featuredCompanies.map(company => (
              <Link
                key={company.name}
                href={`/companies/${company.name.toLowerCase().replace(/\s+/g, '-')}`}
                className="group relative rounded-lg bg-white p-6 shadow-sm transition-shadow hover:shadow-md"
              >
                <div className="aspect-h-2 aspect-w-3 mb-4">
                  <div className="h-16 w-16 rounded-full bg-gray-200"></div>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 group-hover:text-indigo-600">
                  {company.name}
                </h3>
                <p className="mt-2 text-sm text-gray-500">
                  {company.jobCount} open positions
                </p>
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="bg-white py-16">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            <div className="text-center">
              <div className="mx-auto h-12 w-12 text-indigo-600">
                <BriefcaseIcon />
              </div>
              <h3 className="mt-6 text-lg font-semibold text-gray-900">
                Thousands of Jobs
              </h3>
              <p className="mt-2 text-gray-500">
                Find opportunities across various industries and roles
              </p>
            </div>
            <div className="text-center">
              <div className="mx-auto h-12 w-12 text-indigo-600">
                <BuildingOfficeIcon />
              </div>
              <h3 className="mt-6 text-lg font-semibold text-gray-900">
                Top Companies
              </h3>
              <p className="mt-2 text-gray-500">
                Connect with leading employers in Qatar
              </p>
            </div>
            <div className="text-center">
              <div className="mx-auto h-12 w-12 text-indigo-600">
                <MagnifyingGlassIcon />
              </div>
              <h3 className="mt-6 text-lg font-semibold text-gray-900">
                Smart Job Search
              </h3>
              <p className="mt-2 text-gray-500">
                Advanced filters to find your perfect match
              </p>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
