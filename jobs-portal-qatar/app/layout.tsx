import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import Navbar from "@/components/layout/Navbar";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Jobs Portal Qatar - Find Your Dream Job",
  description: "Discover thousands of job opportunities with top companies in Qatar. Your next career move starts here.",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ClerkProvider>
      <html lang="en" suppressHydrationWarning>
        <body className={inter.className} suppressHydrationWarning>
          <Navbar />
          {children}
        </body>
      </html>
    </ClerkProvider>
  );
}
