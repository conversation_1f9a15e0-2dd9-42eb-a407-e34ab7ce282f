import React from 'react';
import { UseFormRegisterReturn } from 'react-hook-form';
import { Input } from '@/components/common';

interface FormFieldProps {
  label: string;
  error?: string | undefined;
  helperText?: string | undefined;
  required?: boolean | undefined;
  children?: React.ReactNode;
}

export const FormField: React.FC<FormFieldProps> = ({
  label,
  error,
  helperText,
  required,
  children,
}) => {
  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-neutral-700">
        {label}
        {required && <span className="ml-1 text-error-500">*</span>}
      </label>
      {children}
      {error && <p className="text-sm text-error-600">{error}</p>}
      {helperText && !error && (
        <p className="text-sm text-neutral-500">{helperText}</p>
      )}
    </div>
  );
};

interface FormInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  error?: string;
  helperText?: string;
  registration?: UseFormRegisterReturn;
}

export const FormInput: React.FC<FormInputProps> = ({
  label,
  error,
  helperText,
  registration,
  required,
  ...props
}) => {
  return (
    <FormField
      label={label}
      error={error}
      helperText={helperText}
      required={required}
    >
      <Input {...props} {...registration} error={error} required={required} />
    </FormField>
  );
};

interface FormTextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label: string;
  error?: string;
  helperText?: string;
  registration?: UseFormRegisterReturn;
}

export const FormTextarea: React.FC<FormTextareaProps> = ({
  label,
  error,
  helperText,
  registration,
  required,
  className,
  ...props
}) => {
  return (
    <FormField
      label={label}
      error={error}
      helperText={helperText}
      required={required}
    >
      <textarea
        {...props}
        {...registration}
        className={`flex min-h-[80px] w-full rounded-lg border border-neutral-300 bg-white px-3 py-2 text-sm placeholder:text-neutral-500 focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500/20 disabled:cursor-not-allowed disabled:opacity-50 ${
          error
            ? 'border-error-500 focus:border-error-500 focus:ring-error-500/20'
            : ''
        } ${className || ''}`}
        required={required}
      />
    </FormField>
  );
};

interface FormSelectProps
  extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label: string;
  error?: string;
  helperText?: string;
  registration?: UseFormRegisterReturn;
  options: { value: string; label: string }[];
  placeholder?: string;
}

export const FormSelect: React.FC<FormSelectProps> = ({
  label,
  error,
  helperText,
  registration,
  options,
  placeholder,
  required,
  className,
  ...props
}) => {
  return (
    <FormField
      label={label}
      error={error}
      helperText={helperText}
      required={required}
    >
      <select
        {...props}
        {...registration}
        className={`flex h-10 w-full rounded-lg border border-neutral-300 bg-white px-3 py-2 text-sm focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500/20 disabled:cursor-not-allowed disabled:opacity-50 ${
          error
            ? 'border-error-500 focus:border-error-500 focus:ring-error-500/20'
            : ''
        } ${className || ''}`}
        required={required}
      >
        {placeholder && (
          <option value="" disabled>
            {placeholder}
          </option>
        )}
        {options.map(option => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </FormField>
  );
};

interface FormCheckboxProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  error?: string;
  helperText?: string;
  registration?: UseFormRegisterReturn;
}

export const FormCheckbox: React.FC<FormCheckboxProps> = ({
  label,
  error,
  helperText,
  registration,
  className,
  ...props
}) => {
  return (
    <div className="space-y-2">
      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          {...props}
          {...registration}
          className={`h-4 w-4 rounded border-neutral-300 text-primary-600 focus:ring-primary-500 ${className || ''}`}
        />
        <label className="text-sm font-medium text-neutral-700">{label}</label>
      </div>
      {error && <p className="text-sm text-error-600">{error}</p>}
      {helperText && !error && (
        <p className="text-sm text-neutral-500">{helperText}</p>
      )}
    </div>
  );
};
