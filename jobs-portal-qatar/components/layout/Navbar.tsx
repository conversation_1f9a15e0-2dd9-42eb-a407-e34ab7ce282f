'use client';

import { Disclosure } from '@headlessui/react';
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { User<PERSON>utton, SignInButton, SignedIn, SignedOut } from '@clerk/nextjs';
import { Button } from '@/components/common';
import { cn } from '@/lib/utils';
import { APP_NAME, ROUTES } from '@/constants';

const navigation = [
  { name: 'Home', href: ROUTES.HOME },
  { name: 'Find Jobs', href: ROUTES.JOBS.SEARCH },
  { name: 'Companies', href: '/companies' },
  { name: 'Career Resources', href: '/resources' },
];

export default function Navbar() {
  const pathname = usePathname();

  return (
    <Disclosure
      as="nav"
      className="border-b border-neutral-200 bg-white shadow-sm"
    >
      {({ open }) => (
        <>
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="flex h-16 justify-between">
              <div className="flex">
                <div className="flex flex-shrink-0 items-center">
                  <Link
                    href={ROUTES.HOME}
                    className="text-2xl font-bold text-primary-600 transition-colors hover:text-primary-700"
                  >
                    {APP_NAME}
                  </Link>
                </div>
                <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
                  {navigation.map(item => (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={cn(
                        pathname === item.href
                          ? 'border-primary-500 text-neutral-900'
                          : 'border-transparent text-neutral-600 hover:border-neutral-300 hover:text-neutral-900',
                        'inline-flex items-center border-b-2 px-1 pt-1 text-sm font-medium transition-colors'
                      )}
                    >
                      {item.name}
                    </Link>
                  ))}
                </div>
              </div>
              <div className="hidden sm:ml-6 sm:flex sm:items-center">
                <SignedIn>
                  <UserButton afterSignOutUrl={ROUTES.HOME} />
                </SignedIn>
                <SignedOut>
                  <div className="flex items-center space-x-4">
                    <SignInButton mode="modal">
                      <Button variant="ghost" size="sm">
                        Sign in
                      </Button>
                    </SignInButton>
                    <Link href={ROUTES.AUTH.SIGN_UP}>
                      <Button size="sm">Register</Button>
                    </Link>
                  </div>
                </SignedOut>
              </div>
              <div className="-mr-2 flex items-center sm:hidden">
                <Disclosure.Button className="inline-flex items-center justify-center rounded-lg p-2 text-neutral-400 hover:bg-neutral-100 hover:text-neutral-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500">
                  <span className="sr-only">Open main menu</span>
                  {open ? (
                    <XMarkIcon className="block h-6 w-6" aria-hidden="true" />
                  ) : (
                    <Bars3Icon className="block h-6 w-6" aria-hidden="true" />
                  )}
                </Disclosure.Button>
              </div>
            </div>
          </div>

          <Disclosure.Panel className="sm:hidden">
            <div className="space-y-1 pb-3 pt-2">
              {navigation.map(item => (
                <Disclosure.Button
                  key={item.name}
                  as={Link}
                  href={item.href}
                  className={cn(
                    pathname === item.href
                      ? 'border-primary-500 bg-primary-50 text-primary-700'
                      : 'border-transparent text-neutral-600 hover:border-neutral-300 hover:bg-neutral-50 hover:text-neutral-900',
                    'block border-l-4 py-2 pl-3 pr-4 text-base font-medium transition-colors'
                  )}
                >
                  {item.name}
                </Disclosure.Button>
              ))}
            </div>
            <div className="border-t border-neutral-200 pb-3 pt-4">
              <SignedIn>
                <div className="flex items-center px-4">
                  <UserButton afterSignOutUrl={ROUTES.HOME} />
                </div>
              </SignedIn>
              <SignedOut>
                <div className="space-y-2 px-4">
                  <SignInButton mode="modal">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start"
                    >
                      Sign in
                    </Button>
                  </SignInButton>
                  <Disclosure.Button as={Link} href={ROUTES.AUTH.SIGN_UP}>
                    <Button size="sm" className="w-full">
                      Register
                    </Button>
                  </Disclosure.Button>
                </div>
              </SignedOut>
            </div>
          </Disclosure.Panel>
        </>
      )}
    </Disclosure>
  );
}
