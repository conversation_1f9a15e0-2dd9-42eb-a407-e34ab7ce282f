{"name": "jobs-portal-qatar", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "analyze": "cross-env ANALYZE=true next build", "prepare": "husky"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@clerk/nextjs": "^6.9.6", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.9.1", "@prisma/client": "^6.1.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "bcryptjs": "^2.4.3", "clsx": "^2.1.1", "next": "^15.1.3", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "tailwind-merge": "^3.3.1", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@next/bundle-analyzer": "^15.4.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/bcryptjs": "^2.4.6", "@types/node": "^22.10.5", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "eslint": "^9.17.0", "eslint-config-next": "^15.1.3", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "husky": "^9.1.7", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "lint-staged": "^16.1.2", "postcss": "^8.4.49", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "prisma": "^6.1.0", "tailwindcss": "^3.4.17", "tsx": "^4.20.3", "typescript": "^5.7.2"}}