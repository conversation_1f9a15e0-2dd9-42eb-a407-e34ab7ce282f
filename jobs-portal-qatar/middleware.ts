import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';

// Define protected routes
const isProtectedRoute = createRouteMatcher([
  '/dashboard(.*)',
  '/profile(.*)',
  '/applications(.*)',
  '/saved-jobs(.*)',
  '/job-alerts(.*)',
  '/onboarding(.*)',
  '/jobs/post(.*)',
  '/jobs/manage(.*)',
  '/api/auth/(.*)',
  '/api/users/(.*)',
  '/api/applications/(.*)',
  '/api/notifications/(.*)',
]);

// Define public API routes
const isPublicApiRoute = createRouteMatcher([
  '/api/webhooks/(.*)',
  '/api/public/(.*)',
  '/api/jobs/search',
  '/api/companies/search',
]);

export default clerkMiddleware((auth, req) => {
  // Allow public API routes without authentication
  if (isPublicApiRoute(req)) {
    return;
  }

  // Protect routes that require authentication
  if (isProtectedRoute(req)) {
    auth().protect();
  }
});

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
};
