{"openapi": "3.0.0", "info": {"title": "Admin API", "version": "1.0.0", "description": "Administrative endpoints for the Jobs Portal"}, "servers": [{"url": "/api/v1"}], "paths": {"/admin/users": {"get": {"tags": ["User Management"], "summary": "Get all users", "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "userType", "schema": {"type": "string", "enum": ["jobseeker", "employer", "admin"]}}, {"in": "query", "name": "status", "schema": {"type": "string", "enum": ["active", "suspended", "pending"]}}, {"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 20}}], "responses": {"200": {"description": "Users retrieved successfully"}, "403": {"description": "Insufficient permissions"}}}}, "/admin/users/{userId}": {"put": {"tags": ["User Management"], "summary": "Update user status", "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "userId", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "enum": ["active", "suspended"]}, "reason": {"type": "string"}}, "required": ["status"]}}}}, "responses": {"200": {"description": "User status updated successfully"}, "404": {"description": "User not found"}}}}, "/admin/companies": {"get": {"tags": ["Company Management"], "summary": "Get all companies", "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "status", "schema": {"type": "string", "enum": ["pending", "approved", "rejected"]}}], "responses": {"200": {"description": "Companies retrieved successfully"}}}}, "/admin/companies/{companyId}/verify": {"post": {"tags": ["Company Management"], "summary": "Verify company registration", "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "companyId", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "enum": ["approved", "rejected"]}, "comments": {"type": "string"}}, "required": ["status"]}}}}, "responses": {"200": {"description": "Company verification status updated"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}