{"openapi": "3.0.0", "info": {"title": "Authentication API", "version": "1.0.0", "description": "Authentication endpoints for the Jobs Portal"}, "servers": [{"url": "/api/v1"}], "paths": {"/auth/register": {"post": {"tags": ["Authentication"], "summary": "Register a new user", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string", "format": "password"}, "userType": {"type": "string", "enum": ["jobseeker", "employer"]}}, "required": ["email", "password", "userType"]}}}}, "responses": {"201": {"description": "User registered successfully"}, "400": {"description": "Invalid input"}}}}, "/auth/login": {"post": {"tags": ["Authentication"], "summary": "Login user", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string", "format": "password"}}, "required": ["email", "password"]}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"token": {"type": "string"}}}}}}, "401": {"description": "Invalid credentials"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}