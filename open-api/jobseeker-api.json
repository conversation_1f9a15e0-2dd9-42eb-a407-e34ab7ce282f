{"openapi": "3.0.0", "info": {"title": "Job Seeker API", "version": "1.0.0", "description": "API for managing job seeker functionalities"}, "servers": [{"url": "/api/v1"}], "paths": {"/jobseeker/profile": {"get": {"tags": ["JobSeeker Profile"], "summary": "Get jobseeker profile", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Profile retrieved successfully"}, "401": {"description": "Unauthorized"}}}, "put": {"tags": ["JobSeeker Profile"], "summary": "Update jobseeker profile", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"personalInfo": {"type": "object", "properties": {"firstName": {"type": "string"}, "lastName": {"type": "string"}, "dateOfBirth": {"type": "string", "format": "date"}, "nationality": {"type": "string"}}}, "contactInfo": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "phone": {"type": "string"}, "address": {"type": "string"}}}, "professionalInfo": {"type": "object", "properties": {"skills": {"type": "array", "items": {"type": "string"}}, "experience": {"type": "array", "items": {"type": "object", "properties": {"company": {"type": "string"}, "position": {"type": "string"}, "startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}}}}}}}}}}}, "responses": {"200": {"description": "Profile updated successfully"}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}}}}, "/jobseeker/resumes": {"post": {"tags": ["Resume Management"], "summary": "Upload a new resume", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}, "title": {"type": "string"}}}}}}, "responses": {"201": {"description": "Resume uploaded successfully"}, "400": {"description": "Invalid file format"}}}, "get": {"tags": ["Resume Management"], "summary": "Get all resumes", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "List of resumes retrieved successfully"}}}}, "/api/user/visibility": {"patch": {"summary": "Update profile visibility", "description": "Update the visibility settings of the user's profile.", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"visibility": {"type": "string", "enum": ["public", "private", "partially_visible"]}, "sections": {"type": "array", "items": {"type": "string"}}}}}}}, "responses": {"200": {"description": "Profile visibility updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}}}}, "/api/resumes/build": {"post": {"summary": "Create a resume", "description": "Create a resume using the resume builder tool.", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"personalDetails": {"type": "object"}, "education": {"type": "array", "items": {"type": "object"}}, "workExperience": {"type": "array", "items": {"type": "object"}}, "skills": {"type": "array", "items": {"type": "string"}}}}}}}, "responses": {"200": {"description": "Resume created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"resumeId": {"type": "string"}, "message": {"type": "string"}}}}}}}}}, "/api/alerts": {"get": {"summary": "Get job alerts", "description": "Retrieve all job alerts created by the user.", "responses": {"200": {"description": "List of job alerts", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"alertId": {"type": "string"}, "name": {"type": "string"}, "criteria": {"type": "object"}}}}}}}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}