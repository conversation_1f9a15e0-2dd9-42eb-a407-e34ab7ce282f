{"openapi": "3.0.0", "info": {"title": "Jobs API", "version": "1.0.0", "description": "Job search and application endpoints"}, "servers": [{"url": "/api/v1"}], "paths": {"/jobs/search": {"get": {"tags": ["Job Search"], "summary": "Search for jobs", "parameters": [{"in": "query", "name": "keywords", "schema": {"type": "string"}, "description": "Search keywords (job title, skills, company)"}, {"in": "query", "name": "location", "schema": {"type": "string"}}, {"in": "query", "name": "employmentType", "schema": {"type": "string", "enum": ["full-time", "part-time", "contract", "temporary"]}}, {"in": "query", "name": "salaryMin", "schema": {"type": "number"}}, {"in": "query", "name": "salaryMax", "schema": {"type": "number"}}, {"in": "query", "name": "industry", "schema": {"type": "string"}, "description": "Filter jobs based on industry"}, {"in": "query", "name": "experienceLevel", "schema": {"type": "string", "enum": ["entry", "mid", "senior", "lead"]}, "description": "Filter jobs by required experience level"}, {"in": "query", "name": "posted<PERSON><PERSON>in", "schema": {"type": "integer"}, "description": "Number of days since the job was posted"}, {"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 20}}], "responses": {"200": {"description": "Search results retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"jobs": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "company": {"type": "string"}, "location": {"type": "string"}, "industry": {"type": "string"}, "experienceLevel": {"type": "string"}, "salary": {"type": "object", "properties": {"min": {"type": "number"}, "max": {"type": "number"}, "currency": {"type": "string"}}}, "postedDate": {"type": "string", "format": "date-time"}}}}, "total": {"type": "integer"}, "page": {"type": "integer"}, "pages": {"type": "integer"}}}}}}}}}, "/jobs/{jobId}/apply": {"post": {"tags": ["Job Application"], "summary": "Apply for a job", "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "jobId", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"resumeId": {"type": "string"}, "coverLetter": {"type": "string"}, "additionalInfo": {"type": "object"}}, "required": ["resumeId"]}}}}, "responses": {"201": {"description": "Application submitted successfully"}, "400": {"description": "Invalid input"}, "404": {"description": "Job not found"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}