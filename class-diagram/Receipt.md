# Receipt Class

## Description
Represents a payment receipt generated after successful transaction.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `receiptNumber` (private, String): Unique receipt number
- `transactionId` (private, int): Reference to Transaction
- `issueDate` (private, Date): Date receipt was issued
- `payerId` (private, int): ID of payer

### Payment Details
- `amount` (private, BigDecimal): Amount paid
- `currency` (private, String): Currency code
- `paymentMethod` (private, String): Method of payment
- `paymentReference` (private, String): Payment reference number
- `description` (private, String): Payment description

### Additional Information
- `items` (private, List<ReceiptItem>): Itemized receipt details
- `taxDetails` (private, Map<String, BigDecimal>): Tax breakdown
- `metadata` (private, Map<String, Object>): Additional receipt data
- `status` (private, String): Receipt status
- `notes` (private, String): Additional notes

## Methods
### Receipt Management
- `generate()`: Generates new receipt
- `void()`: Voids the receipt
- `duplicate()`: Creates duplicate copy
- `addItem(ReceiptItem item)`: Adds receipt item
- `calculateTotals()`: Calculates receipt totals

### Document Generation
- `generatePDF()`: Generates PDF version
- `generateDigitalCopy()`: Creates digital copy
- `emailReceipt(String email)`: Sends receipt via email
- `printReceipt()`: Prints physical copy

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two Receipt objects
- `hashCode()`: Generates hash code

## Relationships
- References one `Transaction`
- References one `User` as payer
- Has many `ReceiptItem` objects 