# ModerationItem Class

## Description
Manages content moderation workflow for various types of content in the system.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `contentType` (private, String): Type of content ("JobPosting", "Resume", etc.)
- `contentId` (private, int): ID of the content being moderated
- `moderatorId` (private, int): ID of assigned moderator
- `createdAt` (private, Date): Creation date
- `updatedAt` (private, Date): Last update date

### Moderation Details
- `status` (private, String): Moderation status (Pending, Approved, Rejected)
- `priority` (private, String): Moderation priority (High, Medium, Low)
- `reason` (private, String): Reason for moderation
- `notes` (private, String): Moderator's notes
- `decision` (private, String): Final decision
- `decisionReason` (private, String): Reason for decision

### Flags
- `hasInappropriateContent` (private, boolean): Flag for inappropriate content
- `requiresManualReview` (private, boolean): Flag for manual review
- `isAutoModerated` (private, boolean): Flag for automatic moderation

## Methods
### Moderation Management
- `assignModerator(int moderatorId)`: Assigns a moderator
- `approve(String notes)`: Approves the content
- `reject(String reason)`: Rejects the content
- `requestAdditionalReview()`: Requests additional review
- `addNote(String note)`: Adds moderator note

### Status Management
- `updateStatus(String status)`: Updates moderation status
- `escalate(String reason)`: Escalates to higher priority
- `setPriority(String priority)`: Sets moderation priority

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two ModerationItem objects
- `hashCode()`: Generates hash code

## Relationships
- References one content object (JobPosting, Resume, etc.)
- References one `User` as moderator
- Has many `ModerationNote` objects 