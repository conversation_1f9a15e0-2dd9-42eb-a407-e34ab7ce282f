# Transaction Class

## Description
Manages payment transactions in the system.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `payerId` (private, int): ID of user making payment
- `receiverId` (private, int): ID of payment recipient
- `createdAt` (private, Date): Transaction creation timestamp
- `completedAt` (private, Date): Transaction completion timestamp

### Payment Details
- `amount` (private, BigDecimal): Transaction amount
- `currency` (private, String): Currency code
- `paymentMethod` (private, String): Payment method used
- `paymentGatewayId` (private, String): External payment gateway ID
- `status` (private, String): Transaction status
- `type` (private, String): Transaction type (subscription, job posting, etc.)

### Additional Info
- `description` (private, String): Transaction description
- `metadata` (private, Map<String, Object>): Additional transaction data
- `refundStatus` (private, String): Refund status if applicable
- `failureReason` (private, String): Reason if transaction failed

## Methods
### Transaction Processing
- `process()`: Processes the transaction
- `validate()`: Validates transaction details
- `complete()`: Marks transaction as complete
- `fail(String reason)`: Marks transaction as failed
- `refund()`: Processes refund

### Financial Calculations
- `calculateFees()`: Calculates transaction fees
- `calculateTax()`: Calculates applicable tax
- `getTotal()`: Gets total amount including fees

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two Transaction objects
- `hashCode()`: Generates hash code

## Relationships
- References two `User` objects (payer and receiver)
- Has one `Invoice`
- May have one `Receipt` 