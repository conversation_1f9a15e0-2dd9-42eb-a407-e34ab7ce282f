# JobPosting Class

## Description
Represents a job posting with detailed job requirements and application management.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `employerId` (private, int): Reference to Employer
- `title` (private, String): Job title
- `department` (private, String): Department name
- `description` (private, String): Detailed job description
- `createdAt` (private, Date): Creation date
- `updatedAt` (private, Date): Last update date
- `expiryDate` (private, Date): Posting expiry date

### Job Details
- `requirements` (private, List<String>): Job requirements
- `qualifications` (private, List<String>): Required qualifications
- `skills` (private, List<String>): Required skills
- `experienceLevel` (private, String): Required experience level
- `employmentType` (private, String): Type of employment
- `workType` (private, String): Work arrangement (remote, onsite, hybrid)
- `location` (private, String): Job location
- `salary` (private, SalaryRange): Salary information
- `benefits` (private, List<String>): Job benefits

### Status and Moderation
- `status` (private, String): Status of posting (draft, published, closed)
- `approvalStatus` (private, String): Approval status (pending, approved, rejected)
- `requiresModeration` (private, boolean): Moderation flag
- `moderationStatus` (private, String): Status of moderation

### Screening
- `screeningQuestions` (private, List<ScreeningQuestion>): Pre-screening questions
- `requiredDocuments` (private, List<String>): Required application documents

## Methods
### Posting Management
- `publish()`: Publishes the job posting
- `close()`: Closes the job posting
- `extend(Date newExpiryDate)`: Extends posting expiry date
- `submitForApproval()`: Submits for approval
- `approve()`: Approves the posting
- `reject(String reason)`: Rejects the posting

### Application Management
- `addScreeningQuestion(ScreeningQuestion question)`: Adds a screening question
- `removeScreeningQuestion(int questionId)`: Removes a screening question
- `getApplications()`: Retrieves all applications
- `getApplicationsWithStatus(String status)`: Retrieves filtered applications

### Analytics
- `getViewCount()`: Returns number of views
- `getApplicationCount()`: Returns number of applications
- `getAnalytics()`: Returns detailed analytics

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two JobPosting objects
- `hashCode()`: Generates hash code

## Relationships
- Belongs to one `Employer`
- Has many `JobApplication` objects
- Has many `ScreeningQuestion` objects
- Has one `JobPostingAnalytics` object
