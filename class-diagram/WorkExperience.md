# WorkExperience Class

## Description
Represents professional work experience in a resume.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `resumeId` (private, int): Reference to Resume
- `createdAt` (private, Date): Creation date
- `updatedAt` (private, Date): Last update date

### Employment Details
- `companyName` (private, String): Name of employer
- `location` (private, String): Job location
- `jobTitle` (private, String): Position title
- `department` (private, String): Department name
- `startDate` (private, Date): Employment start date
- `endDate` (private, Date): Employment end date
- `isCurrentJob` (private, boolean): Current employment status
- `employmentType` (private, String): Type of employment (Full-time, Part-time, etc.)

### Role Details
- `responsibilities` (private, List<String>): Job responsibilities
- `achievements` (private, List<String>): Key achievements
- `skills` (private, List<String>): Skills utilized
- `projects` (private, List<String>): Key projects
- `description` (private, String): Role description
- `toolsUsed` (private, List<String>): Tools and technologies used

## Methods
### Content Management
- `updateCompany(String company)`: Updates company information
- `updatePosition(String title)`: Updates job title
- `updateDates(Date start, Date end)`: Updates employment dates
- `addResponsibility(String responsibility)`: Adds a responsibility
- `removeResponsibility(String responsibility)`: Removes a responsibility
- `addAchievement(String achievement)`: Adds an achievement
- `addSkill(String skill)`: Adds a skill

### Validation
- `validateDates()`: Validates date ranges
- `isComplete()`: Checks if all required fields are filled
- `formatForDisplay()`: Formats experience for display

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two WorkExperience objects
- `hashCode()`: Generates hash code

## Relationships
- Belongs to one `Resume` 