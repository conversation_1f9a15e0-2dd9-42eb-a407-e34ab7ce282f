# SupportTicket Class

## Class Details

- **Class Name:** SupportTicket
- **Attributes:**
  - `id` (private, int): Unique identifier for the support ticket.
  - `userId` (private, int): Unique identifier for the user who created the support ticket.
  - `issueDescription` (private, String): Description of the issue reported in the support ticket.
  - `status` (private, String): Status of the support ticket (e.g., open, in progress, resolved, closed).
  - `createdAt` (private, Date): Date and time when the support ticket was created.
  - `updatedAt` (private, Date): Date and time when the support ticket was last updated.
  - `assignedAdminId` (private, int): Unique identifier for the admin assigned to handle the support ticket.
- **Methods:**
  - `getId()` (public, int): Returns the unique identifier of the support ticket.
  - `getUserId()` (public, int): Returns the unique identifier of the user who created the support ticket.
  - `getIssueDescription()` (public, String): Returns the description of the issue reported in the support ticket.
  - `getStatus()` (public, String): Returns the status of the support ticket.
  - `getCreatedAt()` (public, Date): Returns the creation date and time of the support ticket.
  - `getUpdatedAt()` (public, Date): Returns the last updated date and time of the support ticket.
  - `getAssignedAdminId()` (public, int): Returns the unique identifier of the admin assigned to handle the support ticket.
  - `setId(int id)` (public, void): Sets the unique identifier of the support ticket.
  - `setUserId(int userId)` (public, void): Sets the unique identifier of the user who created the support ticket.
  - `setIssueDescription(String issueDescription)` (public, void): Sets the description of the issue reported in the support ticket.
  - `setStatus(String status)` (public, void): Sets the status of the support ticket.
  - `setCreatedAt(Date createdAt)` (public, void): Sets the creation date and time of the support ticket.
  - `setUpdatedAt(Date updatedAt)` (public, void): Sets the last updated date and time of the support ticket.
  - `setAssignedAdminId(int assignedAdminId)` (public, void): Sets the unique identifier of the admin assigned to handle the support ticket.

## Class Explanation

The `SupportTicket` class represents a support ticket created by a user to report an issue or request assistance. It contains attributes such as `id`, `userId`, `issueDescription`, `status`, `createdAt`, `updatedAt`, and `assignedAdminId`. The class provides getter and setter methods for each attribute, allowing for the retrieval and modification of support ticket information.

### Role and Purpose

The `SupportTicket` class serves as the representation of a support ticket in the system. It encapsulates the details of the support ticket, including the user who created it, the issue description, status, and the admin assigned to handle it. The class ensures that support ticket information is stored securely and provides methods for accessing and updating support ticket details.

### Interactions with Other Classes

The `SupportTicket` class interacts with various other classes in the system, including:

- `User`: Represents the user who created the support ticket.
- `Admin`: Represents the admin assigned to handle the support ticket.

For more details on the interactions, refer to the respective class documentation:
- [User](User.md)
- [Admin](Admin.md)
