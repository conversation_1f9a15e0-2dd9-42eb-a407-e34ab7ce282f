# JobSearchHistory Class

## Description
Tracks and manages user's job search history and preferences.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `userId` (private, int): Reference to User
- `searchTimestamp` (private, Date): Search timestamp
- `sessionId` (private, String): Search session ID
- `searchType` (private, String): Type of search

### Search Parameters
- `keywords` (private, String): Search keywords
- `location` (private, String): Location filter
- `jobType` (private, List<String>): Job types
- `salaryRange` (private, Range): Salary range
- `experienceLevel` (private, String): Experience level
- `remotePreference` (private, String): Remote work preference

### Results Information
- `resultCount` (private, int): Number of results
- `viewedJobs` (private, List<Integer>): Viewed job IDs
- `appliedJobs` (private, List<Integer>): Applied job IDs
- `savedJobs` (private, List<Integer>): Saved job IDs
- `relevanceScore` (private, double): Search relevance score

## Methods
### Search Management
- `saveSearch()`: Saves search criteria
- `updateSearch()`: Updates search parameters
- `deleteSearch()`: Removes search history
- `markAsViewed(int jobId)`: Marks job as viewed
- `trackApplication(int jobId)`: Tracks job application

### Analysis Methods
- `analyzeSearchPattern()`: Analyzes search patterns
- `getSuggestedSearches()`: Gets search suggestions
- `getPopularSearches()`: Gets popular searches
- `calculateRelevance()`: Calculates result relevance

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two JobSearchHistory objects
- `hashCode()`: Generates hash code

## Relationships
- Belongs to one `User`
- References many `JobPosting` objects
- May reference `SavedJob` objects 