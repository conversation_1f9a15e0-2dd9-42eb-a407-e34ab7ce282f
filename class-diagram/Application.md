# Application Class

## Description
Represents a job application submitted by a job seeker for a specific job posting.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `jobSeekerId` (private, int): Reference to JobSeeker
- `jobPostingId` (private, int): Reference to JobPosting
- `resumeId` (private, int): Reference to submitted Resume
- `createdAt` (private, Date): Application submission date
- `updatedAt` (private, Date): Last update date

### Application Content
- `coverLetter` (private, String): Cover letter text
- `typedCoverLetter` (private, String): Cover letter typed directly in application
- `additionalDocuments` (private, List<Document>): Additional uploaded documents
- `screeningAnswers` (private, List<ApplicationAnswer>): Answers to screening questions

### Status and Progress
- `hiringStage` (private, String): Current hiring stage (Applied, Shortlisted, Interviewing, Offered, Hired, Rejected)
- `status` (private, String): Application status
- `lastStatusUpdate` (private, Date): Date of last status change
- `rejectionReason` (private, String): Reason if rejected

### Evaluation
- `rating` (private, int): Employer's rating of application
- `comments` (private, List<ApplicationComment>): Internal comments on application
- `interviewNotes` (private, String): Notes from interviews

## Methods
### Application Management
- `submit()`: Submits the application
- `withdraw()`: Withdraws the application
- `updateStatus(String status)`: Updates application status
- `advanceToNextStage()`: Moves to next hiring stage
- `rejectApplication(String reason)`: Rejects the application

### Content Management
- `addScreeningAnswer(ApplicationAnswer answer)`: Adds screening question answer
- `updateCoverLetter(String coverLetter)`: Updates cover letter
- `addDocument(Document document)`: Adds additional document

### Evaluation Methods
- `addComment(ApplicationComment comment)`: Adds internal comment
- `updateRating(int rating)`: Updates application rating
- `addInterviewNotes(String notes)`: Adds interview notes

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two Application objects
- `hashCode()`: Generates hash code

## Relationships
- Belongs to one `JobSeeker`
- Belongs to one `JobPosting`
- Has one `Resume`
- Has many `ApplicationAnswer` objects
- Has many `ApplicationComment` objects
- Has many `Document` objects
