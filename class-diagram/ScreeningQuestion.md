# ScreeningQuestion Class

## Description
Represents pre-screening questions for job applications.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `jobPostingId` (private, int): Reference to JobPosting
- `questionText` (private, String): The actual question
- `questionType` (private, String): Type of question (text, multiple-choice, yes/no)
- `required` (private, boolean): Whether answer is mandatory
- `order` (private, int): Display order of question

### Question Options
- `options` (private, List<String>): Options for multiple-choice questions
- `correctAnswer` (private, String): Expected/correct answer (if applicable)
- `scoreWeight` (private, int): Weight for scoring responses
- `maxLength` (private, int): Maximum length for text answers
- `hint` (private, String): Hint text for answering

## Methods
### Question Management
- `addOption(String option)`: Adds an option for multiple-choice
- `removeOption(String option)`: Removes an option
- `setCorrectAnswer(String answer)`: Sets correct answer
- `updateQuestionText(String text)`: Updates question text
- `updateQuestionType(String type)`: Updates question type

### Validation
- `validateAnswer(String answer)`: Validates an answer
- `calculateScore(String answer)`: Calculates score for answer
- `isValidQuestionType()`: Validates question type

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two ScreeningQuestion objects
- `hashCode()`: Generates hash code

## Relationships
- Belongs to one `JobPosting`
- Has many `ApplicationAnswer` objects 