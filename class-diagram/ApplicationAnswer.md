# ApplicationAnswer Class

## Description
Represents answers to screening questions in a job application.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `applicationId` (private, int): Reference to Application
- `questionId` (private, int): Reference to ScreeningQuestion
- `answerText` (private, String): The provided answer
- `submittedAt` (private, Date): Submission timestamp

### Evaluation
- `score` (private, int): Score assigned to answer
- `isCorrect` (private, boolean): Whether answer matches correct answer
- `evaluatorNotes` (private, String): Notes from evaluation
- `flags` (private, List<String>): Any flags raised during evaluation
- `status` (private, String): Evaluation status

## Methods
### Answer Management
- `submitAnswer(String answer)`: Submits the answer
- `updateAnswer(String answer)`: Updates submitted answer
- `evaluate()`: Evaluates the answer
- `assignScore(int score)`: Assigns score to answer
- `addEvaluatorNote(String note)`: Adds evaluation note

### Validation
- `validateAnswer()`: Validates answer format/content
- `checkWordLimit()`: Checks if answer meets word limit
- `isComplete()`: Checks if answer is complete

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two ApplicationAnswer objects
- `hashCode()`: Generates hash code

## Relationships
- Belongs to one `Application`
- References one `ScreeningQuestion` 