# Notification Class

## Class Details

- **Class Name:** Notification
- **Attributes:**
  - `id` (private, int): Unique identifier for the notification.
  - `userId` (private, int): Unique identifier for the user receiving the notification.
  - `type` (private, String): Type of the notification (e.g., job match, status update).
  - `message` (private, String): Content of the notification.
  - `status` (private, String): Status of the notification (e.g., read, unread).
  - `createdAt` (private, Date): Date and time when the notification was created.
- **Methods:**
  - `getId()` (public, int): Returns the unique identifier of the notification.
  - `getUserId()` (public, int): Returns the unique identifier of the user receiving the notification.
  - `getType()` (public, String): Returns the type of the notification.
  - `getMessage()` (public, String): Returns the content of the notification.
  - `getStatus()` (public, String): Returns the status of the notification.
  - `getCreatedAt()` (public, Date): Returns the creation date and time of the notification.
  - `setId(int id)` (public, void): Sets the unique identifier of the notification.
  - `setUserId(int userId)` (public, void): Sets the unique identifier of the user receiving the notification.
  - `setType(String type)` (public, void): Sets the type of the notification.
  - `setMessage(String message)` (public, void): Sets the content of the notification.
  - `setStatus(String status)` (public, void): Sets the status of the notification.
  - `setCreatedAt(Date createdAt)` (public, void): Sets the creation date and time of the notification.

## Class Explanation

The `Notification` class represents a notification sent to a user in the system. It contains attributes such as `id`, `userId`, `type`, `message`, `status`, and `createdAt`. The class provides getter and setter methods for each attribute, allowing for the retrieval and modification of notification information.

### Role and Purpose

The `Notification` class serves as the representation of a notification in the system. It encapsulates the details of the notification, including the user receiving it, the type of notification, the content, and the status. The class ensures that notification information is stored securely and provides methods for accessing and updating notification details.

### Interactions with Other Classes

The `Notification` class interacts with various other classes in the system, including:

- `User`: Represents the user receiving the notification.
- `JobSeeker`: Represents a job seeker who may receive notifications related to job matches or application status updates.
- `Employer`: Represents an employer who may receive notifications related to job postings or candidate applications.

For more details on the interactions, refer to the respective class documentation:
- [User](User.md)
- [JobSeeker](JobSeeker.md)
- [Employer](Employer.md)
