# Resume Class

## Description
Manages resume information including education, work experience, and skills.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `jobSeekerId` (private, int): Reference to JobSeeker
- `title` (private, String): Resume title
- `createdAt` (private, Date): Creation date
- `updatedAt` (private, Date): Last update date
- `isActive` (private, boolean): Active status
- `requiresModeration` (private, boolean): Flag for moderation
- `moderationStatus` (private, String): Status of moderation

### Content
- `summary` (private, String): Professional summary
- `education` (private, List<Education>): Educational background
- `workExperience` (private, List<WorkExperience>): Work history
- `skills` (private, List<Skill>): Skills list
- `certifications` (private, List<String>): Professional certifications
- `languages` (private, List<String>): Language proficiencies
- `achievements` (private, List<String>): Professional achievements

## Methods
### Resume Management
- `addEducation(Education education)`: Adds an education entry
- `removeEducation(int educationId)`: Removes an education entry
- `addWorkExperience(WorkExperience workExperience)`: Adds a work experience entry
- `removeWorkExperience(int workExperienceId)`: Removes a work experience entry
- `addSkill(Skill skill)`: Adds a skill entry
- `removeSkill(int skillId)`: Removes a skill entry
- `updateSummary(String summary)`: Updates professional summary

### Moderation
- `submitForModeration()`: Submits resume for review
- `approve()`: Approves the resume
- `reject(String reason)`: Rejects the resume with reason

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two Resume objects
- `hashCode()`: Generates hash code

## Relationships
- Belongs to one `JobSeeker`
- Has many `Education` entries
- Has many `WorkExperience` entries
- Has many `Skill` entries
- Used in many `JobApplication` objects
