# SavedJob Class

## Description
Manages jobs saved by users for later reference.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `userId` (private, int): Reference to User
- `jobId` (private, int): Reference to JobPosting
- `savedAt` (private, Date): Save timestamp
- `expiresAt` (private, Date): Expiry date

### Status Information
- `status` (private, String): Job status (active, expired, filled)
- `isNotified` (private, boolean): Notification status
- `isApplied` (private, boolean): Application status
- `isHidden` (private, boolean): Visibility status
- `notes` (private, String): User notes

### Reminder Settings
- `reminderEnabled` (private, boolean): Reminder status
- `reminderDate` (private, Date): Reminder date
- `reminderType` (private, String): Type of reminder
- `reminderSent` (private, boolean): Reminder sent status
- `customTags` (private, List<String>): User-defined tags

## Methods
### Job Management
- `saveJob()`: Saves the job
- `unsaveJob()`: Removes saved job
- `updateNotes(String notes)`: Updates notes
- `markAsApplied()`: Marks as applied
- `hideJob()`: Hides from view

### Reminder Management
- `setReminder(Date date)`: Sets reminder
- `cancelReminder()`: Cancels reminder
- `snoozeReminder(Duration duration)`: Snoozes reminder
- `checkExpiry()`: Checks job expiry

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two SavedJob objects
- `hashCode()`: Generates hash code

## Relationships
- Belongs to one `User`
- References one `JobPosting`
- May have many `Reminder` objects 