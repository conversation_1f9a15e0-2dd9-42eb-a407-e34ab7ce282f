# JobSeeker Class

## Description
Represents a job seeker user with additional profile information and job search preferences.

## Attributes
### Profile Information
- `id` (private, int): Unique identifier
- `userId` (private, int): Reference to associated User
- `profileVisibility` (private, String): "Public", "Private", or "Partial"
- `firstName` (private, String): First name
- `lastName` (private, String): Last name
- `dateOfBirth` (private, Date): Date of birth
- `currentLocation` (private, String): Current location
- `preferredLocations` (private, List<String>): Preferred work locations
- `profilePicture` (private, String): URL to profile picture
- `summary` (private, String): Professional summary

### Job Preferences
- `desiredJobTitles` (private, List<String>): Preferred job titles
- `desiredSalary` (private, Range): Desired salary range
- `preferredWorkTypes` (private, List<String>): Preferred work types (remote, onsite, hybrid)
- `preferredIndustries` (private, List<String>): Preferred industries
- `yearsOfExperience` (private, int): Total years of experience
- `availableFrom` (private, Date): Date available to start work

## Methods
### Profile Management
- `setProfileVisibility(String visibility)`: Sets the profile visibility
- `updateProfile(ProfileUpdateDTO profile)`: Updates profile information
- `addPreferredLocation(String location)`: Adds a preferred work location
- `removePreferredLocation(String location)`: Removes a preferred work location

### Job Search
- `updateJobPreferences(JobPreferencesDTO preferences)`: Updates job search preferences
- `setAvailability(Date date)`: Sets availability date
- `updateDesiredSalary(Range salaryRange)`: Updates desired salary range

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two JobSeeker objects
- `hashCode()`: Generates hash code

## Relationships
- Belongs to one `User`
- Has many `Resume` objects
- Has many `JobApplication` objects
- Has many `JobAlert` objects
