# ProfileVisibility Class

## Description
Manages visibility settings for job seeker profiles and their sections.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `jobSeekerId` (private, int): Reference to JobSeeker
- `createdAt` (private, Date): Creation timestamp
- `updatedAt` (private, Date): Last update timestamp

### Visibility Settings
- `overallVisibility` (private, String): Overall profile visibility (public, private, connections)
- `searchVisibility` (private, boolean): Visibility in search results
- `employerVisibility` (private, boolean): Visibility to employers
- `networkVisibility` (private, boolean): Visibility to network
- `anonymousMode` (private, boolean): Anonymous profile mode

### Section Visibility
- `sectionSettings` (private, Map<String, String>): Per-section visibility
- `customSections` (private, Map<String, String>): Custom section visibility
- `excludedCompanies` (private, List<Integer>): Blocked company IDs
- `allowedCompanies` (private, List<Integer>): Specifically allowed companies
- `restrictedFields` (private, List<String>): Hidden fields

## Methods
### Visibility Management
- `updateOverallVisibility(String level)`: Updates overall visibility
- `setSectionVisibility(String section, String level)`: Sets section visibility
- `toggleAnonymousMode(boolean enabled)`: Toggles anonymous mode
- `blockCompany(int companyId)`: Blocks company from viewing
- `allowCompany(int companyId)`: Specifically allows company

### Access Control
- `canViewProfile(int viewerId)`: Checks if viewer can see profile
- `canViewSection(int viewerId, String section)`: Checks section visibility
- `isFieldVisible(String field, int viewerId)`: Checks field visibility
- `getVisibleSections(int viewerId)`: Gets visible sections

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two ProfileVisibility objects
- `hashCode()`: Generates hash code

## Relationships
- Belongs to one `JobSeeker`
- References many `Company` objects through lists 