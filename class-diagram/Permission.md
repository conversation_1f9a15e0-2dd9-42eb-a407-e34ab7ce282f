# Permission Class

## Description
Defines specific permissions and access rights within the system.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `name` (private, String): Permission name
- `code` (private, String): Unique permission code
- `description` (private, String): Permission description
- `category` (private, String): Permission category (user, job, admin, etc.)

### Access Details
- `resource` (private, String): Resource being accessed
- `action` (private, String): Allowed action (create, read, update, delete)
- `scope` (private, String): Permission scope
- `isSystem` (private, boolean): Whether it's a system permission
- `conditions` (private, Map<String, Object>): Conditional restrictions

## Methods
### Permission Management
- `validateAccess(User user)`: Validates user access
- `checkConditions(Map<String, Object> context)`: Checks conditional access
- `combineWith(Permission other)`: Combines with another permission
- `isCompatibleWith(Permission other)`: Checks compatibility

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two Permission objects
- `hashCode()`: Generates hash code

## Relationships
- Referenced by many `Role` objects
- Used in access control checks throughout the system 