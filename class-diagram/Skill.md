# Skill Class

## Description
Represents a professional skill or competency in a resume.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `resumeId` (private, int): Reference to Resume
- `name` (private, String): Name of the skill
- `category` (private, String): Skill category (Technical, Soft Skill, Language, etc.)
- `proficiency` (private, String): Proficiency level (Beginner, Intermediate, Expert)
- `yearsOfExperience` (private, int): Years of experience with the skill

### Additional Details
- `certifications` (private, List<String>): Related certifications
- `lastUsed` (private, Date): Date skill was last used
- `endorsements` (private, int): Number of endorsements
- `description` (private, String): Additional description or context
- `isHighlighted` (private, boolean): Flag for highlighting key skills

## Methods
### Skill Management
- `updateProficiency(String level)`: Updates proficiency level
- `addCertification(String certification)`: Adds related certification
- `updateYearsOfExperience(int years)`: Updates experience years
- `incrementEndorsements()`: Increases endorsement count
- `setHighlighted(boolean highlight)`: Sets highlight status

### Validation
- `validateProficiencyLevel()`: Validates proficiency level
- `formatForDisplay()`: Formats skill for display

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two Skill objects
- `hashCode()`: Generates hash code

## Relationships
- Belongs to one `Resume`
- May be referenced by many `WorkExperience` objects 