# NotificationTemplate Class

## Description
Manages templates for various types of system notifications.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `name` (private, String): Template name
- `type` (private, String): Notification type (email, sms, in-app)
- `category` (private, String): Template category
- `isActive` (private, boolean): Active status

### Content
- `subject` (private, String): Subject line for emails
- `body` (private, String): Template content
- `variables` (private, List<String>): Template variables
- `htmlContent` (private, String): HTML version for emails
- `plainTextContent` (private, String): Plain text version

### Configuration
- `language` (private, String): Template language
- `sender` (private, String): Sender information
- `replyTo` (private, String): Reply-to address
- `attachments` (private, List<String>): Default attachments
- `metadata` (private, Map<String, Object>): Additional settings

## Methods
### Template Management
- `createTemplate()`: Creates new template
- `updateTemplate()`: Updates existing template
- `validateTemplate()`: Validates template structure
- `parseVariables()`: Extracts template variables
- `renderTemplate(Map<String, Object> data)`: Renders with data

### Version Control
- `saveVersion()`: Saves template version
- `revertToVersion(String version)`: Reverts to previous version
- `getVersionHistory()`: Gets version history
- `compareVersions(String v1, String v2)`: Compares versions

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two NotificationTemplate objects
- `hashCode()`: Generates hash code

## Relationships
- Referenced by `Notification` system
- May have many versions 