# ApplicationComment Class

## Description
Represents internal comments and feedback on job applications.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `applicationId` (private, int): Reference to Application
- `commenterId` (private, int): ID of user who made comment
- `createdAt` (private, Date): Comment creation timestamp
- `updatedAt` (private, Date): Last update timestamp

### Content
- `comment` (private, String): Comment text
- `visibility` (private, String): Visibility level (team, private, all)
- `category` (private, String): Comment category (feedback, interview notes, general)
- `priority` (private, String): Priority level (high, medium, low)
- `tags` (private, List<String>): Relevant tags

## Methods
### Comment Management
- `addComment(String text)`: Adds comment text
- `updateComment(String text)`: Updates existing comment
- `setVisibility(String level)`: Sets visibility level
- `addTag(String tag)`: Adds a tag
- `removeTag(String tag)`: Removes a tag

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two ApplicationComment objects
- `hashCode()`: Generates hash code

## Relationships
- Belongs to one `Application`
- References one `User` as commenter 