# JobAlert Class

## Description
Manages automated job alerts based on user search criteria.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `userId` (private, int): Reference to User
- `name` (private, String): Alert name
- `createdAt` (private, Date): Creation timestamp
- `status` (private, String): Alert status

### Search Criteria
- `keywords` (private, List<String>): Search keywords
- `locations` (private, List<String>): Location preferences
- `jobTypes` (private, List<String>): Job types
- `industries` (private, List<String>): Industry preferences
- `salaryRange` (private, Range): Salary range
- `experienceLevels` (private, List<String>): Experience levels
- `remotePreference` (private, String): Remote work preference
- `companyTypes` (private, List<String>): Company type preferences

### Notification Settings
- `frequency` (private, String): Alert frequency (daily, weekly)
- `emailNotification` (private, boolean): Email notification preference
- `pushNotification` (private, boolean): Push notification preference
- `lastSentAt` (private, Date): Last notification timestamp
- `matchCount` (private, int): Number of matches found

## Methods
### Alert Management
- `createAlert()`: Creates new alert
- `updateAlert()`: Updates alert criteria
- `deleteAlert()`: Deletes alert
- `pauseAlert()`: Temporarily pauses alert
- `resumeAlert()`: Resumes paused alert

### Notification Processing
- `processAlert()`: Processes alert criteria
- `findMatches()`: Finds matching jobs
- `sendNotification()`: Sends notifications
- `updateLastSent()`: Updates last sent timestamp

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two JobAlert objects
- `hashCode()`: Generates hash code

## Relationships
- Belongs to one `User`
- May reference many `JobPosting` objects
- May generate many `Notification` objects