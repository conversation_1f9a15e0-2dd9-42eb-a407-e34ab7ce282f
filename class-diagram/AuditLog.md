# AuditLog Class

## Description
Tracks system activities and changes for audit purposes.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `timestamp` (private, Date): When the action occurred
- `userId` (private, int): User who performed the action
- `ipAddress` (private, String): IP address of request
- `userAgent` (private, String): Browser/client information

### Action Details
- `action` (private, String): Type of action performed
- `entityType` (private, String): Type of entity affected
- `entityId` (private, String): ID of affected entity
- `status` (private, String): Outcome status
- `description` (private, String): Detailed description

### Change Information
- `previousState` (private, Map<String, Object>): State before change
- `newState` (private, Map<String, Object>): State after change
- `changes` (private, List<String>): List of changed fields
- `reason` (private, String): Reason for change
- `metadata` (private, Map<String, Object>): Additional context

## Methods
### Logging Operations
- `logAction()`: Records the audit entry
- `addMetadata(String key, Object value)`: Adds contextual data
- `setChangeReason(String reason)`: Sets reason for change
- `trackChanges()`: Records state changes

### Query Methods
- `getChangeHistory(String entityType, String entityId)`: Gets entity history
- `getUserActions(int userId)`: Gets user's action history
- `searchLogs(Map<String, Object> criteria)`: Searches audit logs

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two AuditLog objects
- `hashCode()`: Generates hash code

## Relationships
- References one `User` who performed action
- May reference any entity in the system
