# Education Class

## Description
Represents educational background information in a resume.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `resumeId` (private, int): Reference to Resume
- `createdAt` (private, Date): Creation date
- `updatedAt` (private, Date): Last update date

### Institution Details
- `institution` (private, String): Name of educational institution
- `location` (private, String): Location of institution
- `degree` (private, String): Degree obtained
- `fieldOfStudy` (private, String): Field of study/major
- `startDate` (private, Date): Start date of education
- `endDate` (private, Date): End date of education
- `isCurrentlyEnrolled` (private, boolean): Current enrollment status

### Academic Details
- `grade` (private, String): Grade/GPA achieved
- `activities` (private, List<String>): Extracurricular activities
- `achievements` (private, List<String>): Academic achievements
- `description` (private, String): Additional description

## Methods
### Content Management
- `updateInstitution(String institution)`: Updates institution name
- `updateDegree(String degree)`: Updates degree information
- `updateDates(Date start, Date end)`: Updates education dates
- `addActivity(String activity)`: Adds an activity
- `removeActivity(String activity)`: Removes an activity
- `addAchievement(String achievement)`: Adds an achievement

### Validation
- `validateDates()`: Validates date ranges
- `isComplete()`: Checks if all required fields are filled
- `formatForDisplay()`: Formats education entry for display

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two Education objects
- `hashCode()`: Generates hash code

## Relationships
- Belongs to one `Resume` 