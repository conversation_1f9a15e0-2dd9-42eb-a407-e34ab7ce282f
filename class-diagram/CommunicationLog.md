# CommunicationLog Class

## Description
Tracks all communications between employers and job seekers.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `applicationId` (private, int): Reference to Application
- `senderId` (private, int): ID of sender
- `recipientId` (private, int): ID of recipient
- `timestamp` (private, Date): Communication timestamp

### Message Details
- `messageType` (private, String): Type of communication (email, in-app, interview invite)
- `subject` (private, String): Message subject
- `content` (private, String): Message content
- `status` (private, String): Message status (sent, delivered, read)
- `attachments` (private, List<String>): Attached files

### Interview Details
- `interviewDetails` (private, Map<String, Object>): Interview information if applicable
- `scheduledTime` (private, Date): Scheduled interview time
- `location` (private, String): Interview location or link
- `interviewType` (private, String): Type of interview (in-person, video, phone)

## Methods
### Communication Management
- `sendMessage()`: Sends the message
- `updateStatus(String status)`: Updates message status
- `addAttachment(String file)`: Adds an attachment
- `scheduleInterview(Date time)`: Sets interview details
- `cancelCommunication()`: Cancels/revokes message

### Tracking
- `markAsRead()`: Marks message as read
- `trackDeliveryStatus()`: Updates delivery status
- `logInteraction(String type)`: Logs user interaction

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two CommunicationLog objects
- `hashCode()`: Generates hash code

## Relationships
- Belongs to one `Application`
- References two `User` objects (sender and recipient) 