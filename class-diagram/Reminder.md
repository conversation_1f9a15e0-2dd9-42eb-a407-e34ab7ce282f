# Reminder Class

## Description
Manages reminders and notifications for various system events.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `userId` (private, int): Reference to User
- `type` (private, String): Reminder type
- `createdAt` (private, Date): Creation timestamp
- `targetDate` (private, Date): Due/target date

### Reminder Details
- `title` (private, String): Reminder title
- `description` (private, String): Reminder description
- `priority` (private, String): Priority level
- `status` (private, String): Reminder status
- `recurrence` (private, String): Recurrence pattern

### Notification Settings
- `notificationMethod` (private, String): Notification method
- `notificationTime` (private, Date): Scheduled notification time
- `isNotified` (private, boolean): Notification status
- `snoozeCount` (private, int): Times snoozed
- `snoozeInterval` (private, Duration): Snooze duration

## Methods
### Reminder Management
- `createReminder()`: Creates new reminder
- `updateReminder()`: Updates reminder details
- `deleteReminder()`: Deletes reminder
- `markComplete()`: Marks as complete
- `snooze(Duration duration)`: Snoozes reminder

### Notification Handling
- `scheduleNotification()`: Schedules notification
- `sendNotification()`: Sends notification
- `cancelNotification()`: Cancels scheduled notification
- `rescheduleNotification()`: Reschedules notification

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two Reminder objects
- `hashCode()`: Generates hash code

## Relationships
- Belongs to one `User`
- May be associated with various entities (SavedJob, Application, etc.)
- May generate `Notification` objects 