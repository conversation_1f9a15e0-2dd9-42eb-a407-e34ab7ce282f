# Invoice Class

## Description
Represents billing invoices generated for services.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `invoiceNumber` (private, String): Unique invoice number
- `payerId` (private, int): ID of user being billed
- `createdAt` (private, Date): Creation timestamp
- `dueDate` (private, Date): Payment due date

### Billing Details
- `items` (private, List<InvoiceItem>): Line items
- `subtotal` (private, BigDecimal): Sum before tax/fees
- `tax` (private, BigDecimal): Tax amount
- `fees` (private, BigDecimal): Additional fees
- `total` (private, BigDecimal): Total amount
- `currency` (private, String): Currency code

### Status Information
- `status` (private, String): Invoice status
- `paidAt` (private, Date): Payment date
- `paymentMethod` (private, String): Method of payment
- `transactionId` (private, String): Associated transaction ID
- `notes` (private, String): Invoice notes

## Methods
### Invoice Management
- `addItem(InvoiceItem item)`: Adds line item
- `removeItem(InvoiceItem item)`: Removes line item
- `calculateTotals()`: Calculates all totals
- `markAsPaid()`: Updates status to paid
- `void(String reason)`: Voids the invoice

### Document Generation
- `generatePDF()`: Generates PDF version
- `sendToEmail(String email)`: Sends invoice via email
- `createReminder()`: Creates payment reminder

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two Invoice objects
- `hashCode()`: Generates hash code

## Relationships
- References one `User` as payer
- Has many `InvoiceItem` objects
- May have one `Transaction` 