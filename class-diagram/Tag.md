# Tag Class

## Class Details

- **Class Name:** Tag
- **Attributes:**
  - `id` (private, int): Unique identifier for the tag.
  - `name` (private, String): Name of the tag.
  - `createdAt` (private, Date): Date and time when the tag was created.
  - `updatedAt` (private, Date): Date and time when the tag was last updated.
- **Methods:**
  - `getId()` (public, int): Returns the unique identifier of the tag.
  - `getName()` (public, String): Returns the name of the tag.
  - `getCreatedAt()` (public, Date): Returns the creation date and time of the tag.
  - `getUpdatedAt()` (public, Date): Returns the last updated date and time of the tag.
  - `setId(int id)` (public, void): Sets the unique identifier of the tag.
  - `setName(String name)` (public, void): Sets the name of the tag.
  - `setCreatedAt(Date createdAt)` (public, void): Sets the creation date and time of the tag.
  - `setUpdatedAt(Date updatedAt)` (public, void): Sets the last updated date and time of the tag.

## Class Explanation

The `Tag` class represents a tag that can be associated with job postings and resumes. It contains attributes such as `id`, `name`, `createdAt`, and `updatedAt`. The class provides getter and setter methods for each attribute, allowing for the retrieval and modification of tag information.

### Role and Purpose

The `Tag` class serves as the representation of a tag in the system. It encapsulates the details of the tag, including its name and timestamps. The class ensures that tag information is stored securely and provides methods for accessing and updating tag details.

### Interactions with Other Classes

The `Tag` class interacts with various other classes in the system, including:

- `JobPosting`: Represents job postings that can be associated with tags.
- `Resume`: Represents resumes that can be associated with tags.

For more details on the interactions, refer to the respective class documentation:
- [JobPosting](JobPosting.md)
- [Resume](Resume.md)
