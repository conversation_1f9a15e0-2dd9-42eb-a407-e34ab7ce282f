# User Class

## Description
Handles user authentication, profile management, and social media integration.

## Attributes
- `id` (private, int): Unique identifier for the user
- `username` (private, String): Username of the user
- `email` (private, String): Email address of the user
- `password` (private, String): Hashed password for authentication
- `createdAt` (private, Date): Date and time when the user was created
- `updatedAt` (private, Date): Date and time when the user was last updated
- `status` (private, String): Status of the user (e.g., active, suspended)
- `role` (private, String): Role of the user (e.g., jobseeker, employer, admin)
- `googleId` (private, String): User's ID from Google (if linked)
- `linkedinId` (private, String): User's ID from LinkedIn (if linked)
- `facebookId` (private, String): User's ID from Facebook (if linked)
- `passwordResetToken` (private, String): Token for password reset
- `passwordResetTokenExpiry` (private, Date): Expiry of the reset token
- `otp` (private, String): One-time password for mobile verification/login
- `otpExpiry` (private, Date): Expiry of the OTP

## Methods
### Authentication
- `linkSocialAccount(String provider, String providerId)`: Links a social media account
- `unlinkSocialAccount(String provider)`: Unlinks a social media account
- `generatePasswordResetToken()`: Generates and sets a password reset token
- `verifyPasswordResetToken(String token)`: Checks if the token is valid
- `generateOtp()`: Generates and sets a new OTP
- `verifyOtp(String otp)`: Verifies the provided OTP

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation of the user
- `equals(Object obj)`: Compares two User objects
- `hashCode()`: Generates hash code for the User object

## Relationships
- Has many-to-many relationship with `Role` (a user can have multiple roles)
- Has one-to-one relationship with either `JobSeeker` or `Employer` profile
