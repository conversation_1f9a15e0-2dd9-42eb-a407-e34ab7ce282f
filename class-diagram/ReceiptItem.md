# ReceiptItem Class

## Description
Represents individual line items within a receipt.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `receiptId` (private, int): Reference to Receipt
- `description` (private, String): Item description
- `createdAt` (private, Date): Creation timestamp

### Item Details
- `quantity` (private, int): Quantity of item
- `unitPrice` (private, BigDecimal): Price per unit
- `amount` (private, BigDecimal): Total amount
- `taxAmount` (private, BigDecimal): Tax applied
- `discountAmount` (private, BigDecimal): Discount applied
- `finalAmount` (private, BigDecimal): Final amount after tax/discount

### Reference Information
- `serviceId` (private, String): Reference to service provided
- `serviceType` (private, String): Type of service
- `periodStart` (private, Date): Service period start (if applicable)
- `periodEnd` (private, Date): Service period end (if applicable)

## Methods
### Calculation Methods
- `calculateAmount()`: Calculates total amount
- `calculateTax()`: Calculates tax amount
- `applyDiscount(BigDecimal discount)`: Applies discount
- `calculateFinalAmount()`: Calculates final amount

### Validation Methods
- `validate()`: Validates item details
- `validateDates()`: Validates service period dates
- `validateAmounts()`: Validates all monetary amounts

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two ReceiptItem objects
- `hashCode()`: Generates hash code

## Relationships
- Belongs to one `Receipt`
- May reference one `Service` or product 