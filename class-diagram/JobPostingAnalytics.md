# JobPostingAnalytics Class

## Description
Tracks and analyzes performance metrics for job postings.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `jobPostingId` (private, int): Reference to JobPosting
- `startDate` (private, Date): Analytics tracking start date
- `endDate` (private, Date): Analytics tracking end date

### View Metrics
- `views` (private, int): Total number of views
- `uniqueViews` (private, int): Number of unique visitors
- `viewsByDate` (private, Map<Date, Integer>): Daily view counts
- `averageViewDuration` (private, double): Average time spent viewing
- `bounceRate` (private, double): Percentage of immediate exits

### Application Metrics
- `applications` (private, int): Total number of applications
- `applicationsByDate` (private, Map<Date, Integer>): Daily application counts
- `conversionRate` (private, double): Views to applications ratio
- `applicationQualityScore` (private, double): Average application quality
- `applicationsBySource` (private, Map<String, Integer>): Applications by source

### Demographic Data
- `viewerDemographics` (private, Map<String, Object>): Viewer demographics
- `applicantDemographics` (private, Map<String, Object>): Applicant demographics
- `locationData` (private, Map<String, Integer>): Geographic distribution
- `deviceStats` (private, Map<String, Integer>): Device usage statistics

## Methods
### Analytics Processing
- `calculateMetrics()`: Calculates all metrics
- `updateViewCount()`: Increments view count
- `trackApplication()`: Records new application
- `calculateConversionRate()`: Updates conversion rate
- `updateDemographics(Map<String, Object> data)`: Updates demographic data

### Reporting
- `generateReport()`: Generates analytics report
- `getPerformanceMetrics()`: Retrieves key metrics
- `compareWithAverage()`: Compares with category average
- `exportData(String format)`: Exports analytics data

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two JobPostingAnalytics objects
- `hashCode()`: Generates hash code

## Relationships
- Belongs to one `JobPosting`
- May reference many `Application` objects
- May reference many `User` objects for demographic data 