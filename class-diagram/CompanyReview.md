# CompanyReview Class

## Description
Manages company reviews and ratings submitted by users.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `companyId` (private, int): Reference to Company
- `reviewerId` (private, int): Reference to reviewer User
- `createdAt` (private, Date): Review creation date
- `updatedAt` (private, Date): Last update date

### Review Content
- `rating` (private, int): Overall rating (1-5)
- `title` (private, String): Review title
- `content` (private, String): Review content
- `pros` (private, String): Positive aspects
- `cons` (private, String): Negative aspects
- `advice` (private, String): Advice to management

### Detailed Ratings
- `workLifeBalance` (private, int): Work-life balance rating
- `compensation` (private, int): Salary/benefits rating
- `careerGrowth` (private, int): Career opportunities rating
- `management` (private, int): Management rating
- `culture` (private, int): Company culture rating

### Status Information
- `status` (private, String): Review status (pending, approved, rejected)
- `isVerified` (private, boolean): Employment verification status
- `isAnonymous` (private, boolean): Anonymous review flag
- `helpfulVotes` (private, int): Number of helpful votes
- `reportCount` (private, int): Number of reports

## Methods
### Review Management
- `submitReview()`: Submits review for approval
- `updateReview()`: Updates existing review
- `verifyEmployment()`: Verifies reviewer employment
- `moderateContent()`: Moderates review content
- `calculateAverageRating()`: Calculates overall rating

### Interaction Methods
- `markHelpful()`: Marks review as helpful
- `reportReview(String reason)`: Reports inappropriate review
- `respondToReview(String response)`: Adds company response
- `hideReview()`: Hides review from public view

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two CompanyReview objects
- `hashCode()`: Generates hash code

## Relationships
- Belongs to one `Company`
- References one `User` as reviewer
- May have one `CompanyResponse` 