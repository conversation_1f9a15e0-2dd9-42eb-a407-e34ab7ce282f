# UserActivityLog Class

## Description
Tracks user activities and interactions within the system.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `userId` (private, int): Reference to User
- `sessionId` (private, String): User session identifier
- `timestamp` (private, Date): Activity timestamp
- `ipAddress` (private, String): User's IP address

### Activity Details
- `activityType` (private, String): Type of activity
- `page` (private, String): Page where activity occurred
- `action` (private, String): Specific action taken
- `target` (private, String): Target of the action
- `result` (private, String): Outcome of the action

### Context Information
- `deviceInfo` (private, Map<String, String>): User device details
- `browserInfo` (private, String): Browser information
- `location` (private, String): Geographic location
- `referrer` (private, String): Referring page/source
- `duration` (private, long): Activity duration

## Methods
### Activity Tracking
- `logActivity()`: Records activity entry
- `updateDuration()`: Updates activity duration
- `addContext(String key, String value)`: Adds contextual info
- `setResult(String result)`: Sets activity outcome
- `markComplete()`: Marks activity as complete

### Analysis Methods
- `getUserPattern()`: Analyzes user behavior pattern
- `getSessionActivities()`: Gets activities in session
- `calculateEngagement()`: Calculates engagement metrics
- `generateActivityReport()`: Creates activity report

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two UserActivityLog objects
- `hashCode()`: Generates hash code

## Relationships
- Belongs to one `User`
- May be part of one `Session`
- May reference various system entities 