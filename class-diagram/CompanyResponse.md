# CompanyResponse Class

## Description
Represents company's official responses to reviews and feedback.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `reviewId` (private, int): Reference to CompanyReview
- `responderId` (private, int): Reference to company representative
- `createdAt` (private, Date): Response creation date
- `updatedAt` (private, Date): Last update date

### Response Content
- `content` (private, String): Response text
- `position` (private, String): <PERSON><PERSON><PERSON><PERSON>'s position
- `department` (private, String): Resp<PERSON><PERSON>'s department
- `isOfficial` (private, boolean): Official response flag
- `isPublic` (private, boolean): Public visibility flag

### Status Information
- `status` (private, String): Response status
- `moderationStatus` (private, String): Moderation status
- `lastModifiedBy` (private, int): Last modifier's ID
- `version` (private, int): Response version
- `isPinned` (private, boolean): Pinned status

## Methods
### Response Management
- `submitResponse()`: Submits new response
- `updateResponse(String content)`: Updates response
- `publishResponse()`: Makes response public
- `unpublishResponse()`: Makes response private
- `pinResponse()`: Pins response to top

### Moderation
- `submitForModeration()`: Submits for review
- `approve()`: Approves response
- `reject(String reason)`: Rejects response
- `flagInappropriate()`: Flags content

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two CompanyResponse objects
- `hashCode()`: Generates hash code

## Relationships
- Belongs to one `CompanyReview`
- References one `User` as responder
- References one `Company` 