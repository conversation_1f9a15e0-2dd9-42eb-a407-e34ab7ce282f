# JobSearch Class

## Class Details

- **Class Name:** JobSearch
- **Attributes:**
  - `id` (private, int): Unique identifier for the job search.
  - `jobSeekerId` (private, int): Unique identifier for the job seeker.
  - `keywords` (private, String): Keywords used in the job search.
  - `filters` (private, String): Filters applied during the job search.
  - `createdAt` (private, Date): Date and time when the job search was created.
  - `updatedAt` (private, Date): Date and time when the job search was last updated.
- **Methods:**
  - `getId()` (public, int): Returns the unique identifier of the job search.
  - `getJobSeekerId()` (public, int): Returns the unique identifier of the job seeker.
  - `getKeywords()` (public, String): Returns the keywords used in the job search.
  - `getFilters()` (public, String): Returns the filters applied during the job search.
  - `getCreatedAt()` (public, Date): Returns the creation date and time of the job search.
  - `getUpdatedAt()` (public, Date): Returns the last updated date and time of the job search.
  - `setId(int id)` (public, void): Sets the unique identifier of the job search.
  - `setJobSeekerId(int jobSeekerId)` (public, void): Sets the unique identifier of the job seeker.
  - `setKeywords(String keywords)` (public, void): Sets the keywords used in the job search.
  - `setFilters(String filters)` (public, void): Sets the filters applied during the job search.
  - `setCreatedAt(Date createdAt)` (public, void): Sets the creation date and time of the job search.
  - `setUpdatedAt(Date updatedAt)` (public, void): Sets the last updated date and time of the job search.

## Class Explanation

The `JobSearch` class represents a job search performed by a job seeker. It contains attributes such as `id`, `jobSeekerId`, `keywords`, `filters`, `createdAt`, and `updatedAt`. The class provides getter and setter methods for each attribute, allowing for the retrieval and modification of job search information.

### Role and Purpose

The `JobSearch` class serves as the representation of a job search in the system. It encapsulates the details of the job search, including the job seeker who performed it, the keywords used, and the filters applied. The class ensures that job search information is stored securely and provides methods for accessing and updating job search details.

### Interactions with Other Classes

The `JobSearch` class interacts with various other classes in the system, including:

- `JobSeeker`: Represents the job seeker who performed the job search.

For more details on the interactions, refer to the respective class documentation:
- [JobSeeker](JobSeeker.md)
