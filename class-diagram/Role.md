# Role Class

## Description
Defines user roles and their associated permissions in the system.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `name` (private, String): Role name (JobSeeker, Employer, Admin, etc.)
- `description` (private, String): Role description
- `createdAt` (private, Date): Creation timestamp
- `updatedAt` (private, Date): Last update timestamp

### Access Control
- `permissions` (private, Set<Permission>): Associated permissions
- `level` (private, int): Role hierarchy level
- `isSystem` (private, boolean): Whether it's a system role
- `isActive` (private, boolean): Role active status
- `restrictions` (private, Map<String, Object>): Role restrictions

## Methods
### Permission Management
- `addPermission(Permission permission)`: Adds a permission
- `removePermission(Permission permission)`: Removes a permission
- `hasPermission(Permission permission)`: Checks if role has permission
- `clearPermissions()`: Removes all permissions
- `updatePermissions(Set<Permission> permissions)`: Updates permission set

### Role Management
- `activate()`: Activates the role
- `deactivate()`: Deactivates the role
- `setLevel(int level)`: Sets role hierarchy level
- `addRestriction(String key, Object value)`: Adds a restriction

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two Role objects
- `hashCode()`: Generates hash code

## Relationships
- Has many `Permission` objects
- Referenced by many `User` objects 