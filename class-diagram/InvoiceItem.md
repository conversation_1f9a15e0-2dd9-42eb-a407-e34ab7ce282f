# InvoiceItem Class

## Description
Represents individual line items within an invoice.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `invoiceId` (private, int): Reference to Invoice
- `description` (private, String): Item description
- `createdAt` (private, Date): Creation timestamp

### Pricing Details
- `quantity` (private, int): Quantity of item
- `unitPrice` (private, BigDecimal): Price per unit
- `amount` (private, BigDecimal): Total amount (quantity * unitPrice)
- `taxRate` (private, BigDecimal): Tax rate for item
- `taxAmount` (private, BigDecimal): Calculated tax amount
- `discountAmount` (private, BigDecimal): Discount applied

### Product Information
- `productId` (private, String): Reference to product/service
- `productType` (private, String): Type of product/service
- `sku` (private, String): Stock keeping unit
- `metadata` (private, Map<String, Object>): Additional item details

## Methods
### Calculation Methods
- `calculateAmount()`: Calculates total amount
- `calculateTax()`: Calculates tax amount
- `applyDiscount(BigDecimal discount)`: Applies discount
- `updateQuantity(int quantity)`: Updates quantity and recalculates
- `updateUnitPrice(BigDecimal price)`: Updates price and recalculates

### Validation Methods
- `validate()`: Validates item details
- `checkPricing()`: Verifies pricing calculations
- `verifyProduct()`: Verifies product existence

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two InvoiceItem objects
- `hashCode()`: Generates hash code

## Relationships
- Belongs to one `Invoice`
- May reference one `Product` or service 