# Employer Class

## Description
Represents an employer organization with company details and job posting capabilities.

## Attributes
### Company Information
- `id` (private, int): Unique identifier
- `userId` (private, int): Reference to associated User
- `companyName` (private, String): Name of the company
- `companyType` (private, String): Type of company (Government, Private, Multinational)
- `industry` (private, String): Industry sector
- `companySize` (private, String): Size of company (employees range)
- `foundedYear` (private, int): Year company was founded
- `description` (private, String): Company description
- `website` (private, String): Company website URL
- `logo` (private, String): URL to company logo

### Verification Details
- `isVerified` (private, boolean): Company verification status
- `verificationDate` (private, Date): Date when verification was completed
- `businessRegistrationNumber` (private, String): Official registration number
- `taxIdentificationNumber` (private, String): Tax ID number

### Contact Information
- `headquarters` (private, String): Company headquarters location
- `contactEmail` (private, String): Primary contact email
- `contactPhone` (private, String): Primary contact phone
- `socialMediaLinks` (private, Map<String, String>): Social media profiles

## Methods
### Company Management
- `updateCompanyProfile(CompanyProfileDTO profile)`: Updates company information
- `uploadLogo(File logo)`: Updates company logo
- `updateSocialMediaLinks(Map<String, String> links)`: Updates social media links
- `markAsVerified()`: Sets verification status and date

### Job Posting Management
- `createJobPosting(JobPostingDTO posting)`: Creates a new job posting
- `getActiveJobPostings()`: Retrieves all active job postings
- `getApplicationsForJob(int jobId)`: Retrieves applications for a specific job
- `getAnalytics()`: Retrieves company's job posting analytics

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two Employer objects
- `hashCode()`: Generates hash code

## Relationships
- Belongs to one `User`
- Has many `JobPosting` objects
- Has many `JobApplication` objects through JobPostings
- Has many `CompanyReview` objects
