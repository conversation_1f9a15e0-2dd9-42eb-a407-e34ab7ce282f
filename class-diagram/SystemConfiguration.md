# SystemConfiguration Class

## Description
Manages system-wide configuration settings and parameters.

## Attributes
### Basic Information
- `id` (private, int): Unique identifier
- `key` (private, String): Configuration key
- `value` (private, String): Configuration value
- `type` (private, String): Value type (string, number, boolean, json)
- `category` (private, String): Configuration category

### Metadata
- `description` (private, String): Setting description
- `defaultValue` (private, String): Default value
- `isEncrypted` (private, boolean): Whether value is encrypted
- `lastModified` (private, Date): Last modification date
- `modifiedBy` (private, int): User who last modified

### Validation
- `validationRules` (private, Map<String, Object>): Validation rules
- `allowedValues` (private, List<String>): Allowed values if restricted
- `minValue` (private, Double): Minimum value if numeric
- `maxValue` (private, Double): Maximum value if numeric
- `pattern` (private, String): Regex pattern if applicable

## Methods
### Configuration Management
- `setValue(String value)`: Updates configuration value
- `resetToDefault()`: Resets to default value
- `encrypt()`: Encrypts sensitive values
- `decrypt()`: Decrypts sensitive values
- `validate()`: Validates configuration value

### Access Methods
- `getTypedValue()`: Gets value in correct type
- `getDecryptedValue()`: Gets decrypted value
- `isValid()`: Checks if value is valid
- `requiresEncryption()`: Checks if encryption needed

### Standard Methods
- Getters and setters for all attributes
- `toString()`: Returns string representation
- `equals(Object obj)`: Compares two SystemConfiguration objects
- `hashCode()`: Generates hash code

## Relationships
- May be referenced throughout the system
- References one `User` as modifier
