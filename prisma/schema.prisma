datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model Application {
  id                 String               @id @default(auto()) @map("_id") @db.ObjectId
  jobPostingId       String               @db.ObjectId
  jobSeekerId        String               @db.ObjectId
  resumeId           String               @db.ObjectId
  coverLetter        String?
  typedCoverLetter   String?
  screeningAnswers   ScreeningAnswer[]
  additionalDocs     AdditionalDocument[]
  hiringStage        String
  status             String
  rating             Int?
  lastStatusUpdate   DateTime?
  rejectionReason    String?
  interviewNotes     String?
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt

  @@index([jobPostingId, hiringStage])
  @@index([jobSeekerId, createdAt])
  @@index([status, lastStatusUpdate])
  @@index([rating, hiringStage])
}

model ScreeningAnswer {
  questionId   String @db.ObjectId
  answer       String
  score        Int
  evaluatorNotes String?
}

model AdditionalDocument {
  type        String
  name        String
  url         String
  uploadedAt  DateTime
}

model Company {
  id                  String   @id @default(auto()) @map("_id") @db.ObjectId
  name                String
  companyType         String
  industry            String
  companySize         String?
  foundedYear         Int?
  description         String
  website             String?
  logo                String?
  headquarters        String
  locations           String[]
  socialMediaLinks    SocialMediaLinks?
  benefits            String[]
  culture             String?
  verificationStatus  String
  verificationDate    DateTime?
  businessRegNumber   String?
  taxId               String?
  contactEmail        String
  contactPhone        String
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  @@index([name, description])
  @@index([industry, companyType, companySize])
  @@index([verificationStatus, verificationDate])
  @@index([headquarters, industry])
}

model SocialMediaLinks {
  linkedin   String?
  twitter    String?
  facebook   String?
  instagram  String?
  glassdoor  String?
}

model JobPosting {
  id                 String               @id @default(auto()) @map("_id") @db.ObjectId
  employerId         String               @db.ObjectId
  companyId          String               @db.ObjectId
  title              String
  department         String
  description        String
  requirements       String[]
  qualifications     String[]
  skills             String[]
  experienceLevel    String
  employmentType     String
  workType           String
  location           String
  salary             Salary?
  benefits           String[]
  screeningQuestions ScreeningQuestion[]
  requiredDocuments  String[]
  status             String
  approvalStatus     String
  requiresModeration Boolean
  moderationStatus   String
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
  expiresAt          DateTime
  views              Int?
  applications       Int?

  @@index([title, description, skills, requirements])
  @@index([status, approvalStatus, createdAt])
  @@index([employerId, status, createdAt])
  @@index([location, employmentType, workType])
  @@index([expiresAt], map: "expiresAt_TTL")
}

model Salary {
  min      Float
  max      Float
  currency String
  period   String
}

model ScreeningQuestion {
  questionText  String
  questionType  String
  options       String[]
  correctAnswer String?
  required      Boolean
  weight        Int
  order         Int
  maxLength     Int?
  hint          String?
}

model Permission {
  id            String   @id @default(auto()) @map("_id") @db.ObjectId
  code          String
  name          String
  description   String
  category      String
  resource      String
  action        String
  scope         String?
  isSystem      Boolean
  dependencies  String[]
  conditions    Condition?
  metadata      Metadata?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@index([code])
  @@index([category, resource])
  @@index([isSystem, action])
  @@index([name, description])
}

model Condition {
  timeRestrictions   TimeRestrictions?
  locationRestrictions String[]
  ipRestrictions     String[]
  roleRestrictions   String[]
  customRules        Json?
}

model TimeRestrictions {
  days  String[]
  hours Int[]
}

model Metadata {
  icon       String?
  uiSection  String?
  auditLevel String?
  riskLevel  String?
}

model RateLimit {
  id            String   @id @default(auto()) @map("_id") @db.ObjectId
  key           String
  type          String
  identifier    String
  endpoint      String
  limit         Int
  window        Int
  current       Int
  resetAt       DateTime
  overrides     Override?
  quotas        Quota?
  status        String
  metadata      Metadata?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@index([key])
  @@index([identifier, endpoint])
  @@index([type, status])
  @@index([resetAt], map: "resetAt_TTL")
}

model Override {
  burst    Burst?
  throttle Throttle?
  expiry   DateTime?
}

model Burst {
  limit  Int
  window Int
}

model Throttle {
  rate   Int
  period String
}

model Quota {
  daily      DailyQuota?
  monthly    MonthlyQuota?
  concurrent ConcurrentQuota?
}

model DailyQuota {
  limit   Int
  used    Int
  resetAt DateTime
}

model MonthlyQuota {
  limit   Int
  used    Int
  resetAt DateTime
}

model ConcurrentQuota {
  limit   Int
  current Int
}

model ResumeAnalytics {
  id                String               @id @default(auto()) @map("_id") @db.ObjectId
  resumeId          String               @db.ObjectId
  userId            String               @db.ObjectId
  views             Int
  uniqueViews       Int
  viewsByEmployer   EmployerView[]
  searchAppearances Int
  downloadCount     Int
  applicationCount  Int
  interviewRate     Float
  skillMatches      SkillMatch?
  viewsByDate       Json?
  lastViewed        DateTime?
  createdAt         DateTime             @default(now())
  updatedAt         DateTime             @updatedAt

  @@index([resumeId, createdAt])
  @@index([userId, views])
  @@index([interviewRate, applicationCount])
}

model EmployerView {
  employerId   String @db.ObjectId
  companyName  String
  viewCount    Int
  lastViewed   DateTime
  jobPostingId String @db.ObjectId
  actionTaken  String
}

model SkillMatch {
  totalJobsAnalyzed Int
  matchedSkills     Json
}

model Resume {
  id            String   @id @default(auto()) @map("_id") @db.ObjectId
  jobSeekerId   String   @db.ObjectId
  format        String
  template      String?
  content       String
  parsedContent String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  isPrimary     Boolean

  @@index([parsedContent], type: "text")
  @@index([jobSeekerId, isPrimary])
}

model Role {
  id            String   @id @default(auto()) @map("_id") @db.ObjectId
  name          String
  slug          String
  description   String
  permissions   String[]
  isSystem      Boolean
  isActive      Boolean
  level         Int
  scope         Scope?
  metadata      Metadata?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@index([slug])
  @@index([isSystem, isActive])
  @@index([level, name])
  @@index([name, description])
}

model Scope {
  organizations String[]
  departments   String[]
  locations     String[]
  features      String[]
}

model SavedJob {
  id            String   @id @default(auto()) @map("_id") @db.ObjectId
  jobSeekerId   String   @db.ObjectId
  jobPostingId  String   @db.ObjectId
  savedAt       DateTime @default(now())

  @@index([jobSeekerId, jobPostingId])
}

model ScreeningQuestion {
  id            String   @id @default(auto()) @map("_id") @db.ObjectId
  jobPostingId  String   @db.ObjectId
  questionText  String
  questionType  String
  options       String[]
  correctAnswer String?
  required      Boolean
  weight        Int
  order         Int
  maxLength     Int?
  hint          String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@index([jobPostingId, order])
  @@index([jobPostingId, required])
  @@index([questionText], type: "text")
}

model SearchIndex {
  id            String   @id @default(auto()) @map("_id") @db.ObjectId
  name          String
  type          String
  status        String
  version       String
  mappings      Json
  settings      Json
  aliases       String[]
  stats         Json?
  maintenance   Json?
  metadata      Metadata?
  createdBy     String   @db.ObjectId
  updatedBy     String   @db.ObjectId
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@index([type, status])
  @@index([name, version])
  @@index([aliases])
}

model Skill {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  resumeId          String   @db.ObjectId
  name              String
  category          String
  proficiency       String
  yearsOfExperience Int
  lastUsed          DateTime?
  endorsements      Int
  isVerified        Boolean
  projects          String[]
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@index([name], type: "text")
  @@index([resumeId, proficiency])
  @@index([category, name])
  @@index([name, endorsements])
}

model SupportTicket {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  userId            String   @db.ObjectId
  subject           String
  issueDescription  String
  status            String
  priority          String
  category          String
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  assignedAdminId   String   @db.ObjectId
  closedAt          DateTime?

  @@index([status, priority, createdAt])
  @@index([userId, createdAt])
}

model SystemConfiguration {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  key               String
  value             Json
  type              String
  category          String
  description       String?
  environment       String
  isActive          Boolean
  version           Int
  validFrom         DateTime
  validTo           DateTime?
  lastModifiedBy    String   @db.ObjectId
  metadata          Metadata?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@index([key, environment, version], map: "unique_key_env_version")
  @@index([category, isActive])
  @@index([validFrom, validTo])
}

model SystemEvent {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  name              String
  type              String
  source            String
  severity          String
  status            String
  data              Json
  metadata          Metadata?
  handlers          Json?
  correlationId     String
  version           String
  broadcast         Json?
  acknowledgments   Json?
  retries           Json?
  expiresAt         DateTime
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@index([type, status])
  @@index([source, createdAt])
  @@index([correlationId, name])
  @@index([expiresAt], map: "expiresAt_TTL")
}

model SystemMetric {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  name              String
  type              String
  value             Float
  unit              String
  timestamp         DateTime
  interval          String
  component         String
  tags              Json
  dimensions        String[]
  statistics        Json?
  metadata          Metadata?
  alerts            Json?

  @@index([name, timestamp])
  @@index([component, type])
  @@index([tags.environment, tags.service])
  @@index([timestamp], map: "timestamp_TTL")
}

model Tag {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  name              String
  slug              String
  type              String
  parentId          String   @db.ObjectId
  level             Int
  description       String?
  synonyms          String[]
  isActive          Boolean
  priority          Int
  metadata          Metadata?
  usageCount        Int
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@index([slug, type], map: "unique_slug_type")
  @@index([parentId, level])
  @@index([type, isActive, priority])
  @@index([name, synonyms, description], type: "text")
}

model UserActivityLog {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  userId            String   @db.ObjectId
  activityType      String
  entityType        String
  entityId          String   @db.ObjectId
  action            String
  timestamp         DateTime
  sessionId         String
  platform          String
  deviceInfo        Json?
  location          Json?
  metadata          Metadata?
  duration          Int
  status            String
  errorDetails      Json?

  @@index([userId, timestamp])
  @@index([activityType, entityType])
  @@index([sessionId, timestamp])
  @@index([timestamp], map: "timestamp_TTL")
}

model UserPreference {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  userId            String   @db.ObjectId
  notifications     Json
  privacy           Json
  communication     Json
  display           Json?
  jobAlerts         Json?
  accessibility     Json?
  language          String
  timezone          String
  theme             String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@index([userId], map: "unique_userId")
  @@index([userId, updatedAt])
  @@index([language, timezone])
}

model UserSession {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  userId            String   @db.ObjectId
  token             String
  refreshToken      String?
  deviceId          String
  platform          String
  userAgent         String?
  ipAddress         String
  location          Json?
  isActive          Boolean
  lastActivity      DateTime
  expiresAt         DateTime
  metadata          Metadata?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@index([userId, isActive])
  @@index([token, expiresAt])
  @@index([expiresAt], map: "expiresAt_TTL")
  @@index([deviceId, userId])
}

model User {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  username          String
  email             String
  password          String
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  status            String
  role              String
  profile           Json
  lastLogin         DateTime?

  @@index([role, status, createdAt])
  @@index([profile.firstName, profile.lastName, profile.skills], type: "text")
  @@index([profile.resumeId], map: "sparse_resumeId")
}

model Webhook {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  userId            String   @db.ObjectId
  name              String
  url               String
  events            String[]
  status            String
  secret            String
  version           String
  retryConfig       Json?
  filters           Json?
  headers           Json?
  lastDelivery      DateTime?
  errorCount        Int
  metadata          Metadata?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@index([userId, status])
  @@index([events, status])
  @@index([lastDelivery])
  @@index([errorCount])
}

model WorkExperience {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  resumeId          String   @db.ObjectId
  companyName       String
  jobTitle          String
  startDate         DateTime
  endDate           DateTime?
  isCurrent         Boolean
  location          String
  description       String
  achievements      String[]
  skills            String[]
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@index([resumeId, startDate])
  @@index([companyName, jobTitle, description], type: "text")
  @@index([companyName, location])
}
