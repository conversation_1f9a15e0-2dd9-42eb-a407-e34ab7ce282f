// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

datasource db {
  provider = "mongodb"  // Specify the database provider
  url      = env("DATABASE_URL")  // Specify the database URL
}

generator client {
  provider = "prisma-client-js" // This tells Prisma to generate a tailored JS client application used to interact with the database.
}

model User {
  id                     String    @id @default(auto()) @map("_id") @db.ObjectId
  username               String    @unique
  email                  String    @unique
  password               String
  createdAt              DateTime  @default(now())
  updatedAt              DateTime  @updatedAt
  status                 String    @default("active") // active, suspended, unverified
  role                   Role      @relation(fields: [roleId], references: [id])
  roleId                 String    @db.ObjectId
  googleId               String?
  linkedinId             String?
  facebookId             String?
  passwordResetToken     String?
  passwordResetTokenExpiry DateTime?
  otp                    String?
  otpExpiry              DateTime?
  jobSeeker              JobSeeker?
  employer               Employer?
  notifications          Notification[]
  jobAlerts              JobAlert[]
  jobSearches            JobSearch[]
  jobSearchHistory       JobSearchHistory[]
  savedJobs              SavedJob[]
  supportTickets         SupportTicket[]
  transactions           Transaction[] @relation("TransactionPayer")
  receivedTransactions   Transaction[] @relation("TransactionReceiver")
  invoices               Invoice[]
  receipts               Receipt[]
  activityLogs           UserActivityLog[]
  apiKeys                ApiKey[]
  sessions               UserSession[]
  sentCommunications     CommunicationLog[] @relation("Sender")
  receivedCommunications CommunicationLog[] @relation("Recipient")
  moderationItems        ModerationItem[] // Moderator
  comments               ApplicationComment[]
  ratings                ApplicationRating[]
  companyReviews         CompanyReview[] // Reviewer
  companyResponses       CompanyResponse[] // Responder
  reminders              Reminder[]
  auditLogs              AuditLog[]

  @@index([roleId])
}


model UserSession {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  userId       String   @db.ObjectId
  user         User     @relation(fields: [userId], references: [id])
  token        String
  deviceInfo   Json?
  ipAddress    String?
  lastActivity DateTime @default(now())
  expiresAt    DateTime
  isValid      Boolean  @default(true)

  @@index([userId])
  @@index([token])
}

model JobSeeker {
  id                 String    @id @default(auto()) @map("_id") @db.ObjectId
  userId             String    @unique @db.ObjectId
  user               User      @relation(fields: [userId], references: [id])
  profileVisibility  String   @default("public") // Public, Private, Partial
  firstName          String
  lastName           String
  dateOfBirth        DateTime?
  currentLocation    String
  preferredLocations String[]
  profilePicture     String?
  summary            String?
  desiredJobTitles   String[]
  desiredSalary      Range?
  preferredWorkTypes String[]
  preferredIndustries String[]
  yearsOfExperience  Int?
  availableFrom      DateTime?
  resumes            Resume[]
  applications       Application[]
  profileVisibilitySettings ProfileVisibility[]

  @@index([currentLocation])
  @@index([yearsOfExperience])
}

model Employer {
  id                      String           @id @default(auto()) @map("_id") @db.ObjectId
  userId                  String           @unique @db.ObjectId
  user                    User             @relation(fields: [userId], references: [id])
  companyName             String
  companyType             String           // Government, Private, Multinational
  industry                String
  companySize             String           // Size range
  foundedYear             Int?
  description             String?
  website                 String?
  logo                    String?
  isVerified              Boolean          @default(false)
  verificationDate        DateTime?
  businessRegistrationNumber String?
  taxIdentificationNumber String?
  headquarters            String?
  contactEmail            String?
  contactPhone            String?
  socialMediaLinks        Json?
  jobPostings             JobPosting[]
  companyReviews          CompanyReview[]
  companyResponses        CompanyResponse[]

  @@index([companyName])
  @@index([industry])
  @@index([companyType])
  @@index([companySize])
  @@index([isVerified])
}

model JobPosting {
  id                 String               @id @default(auto()) @map("_id") @db.ObjectId
  employerId         String               @db.ObjectId
  employer           Employer             @relation(fields: [employerId], references: [id])
  title              String
  department         String?
  description        String
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
  expiryDate         DateTime
  requirements       String[]
  qualifications     String[]
  skills             String[]
  experienceLevel    String
  employmentType     String               // Full-time, Part-time, Contract, etc.
  workType           String               // Remote, Onsite, Hybrid
  location           String
  salary             SalaryRange?
  benefits           String[]
  status             String               @default("draft") // draft, published, closed
  approvalStatus     String               @default("pending") // pending, approved, rejected
  requiresModeration Boolean              @default(false)
  moderationStatus   String?              // pending, approved, rejected
  applications       Application[]
  screeningQuestions ScreeningQuestion[]
  analytics          JobPostingAnalytics?
  views              Int                  @default(0)
  savedJobs          SavedJob[]

  @@index([employerId])
  @@index([title])
  @@index([status])
  @@index([approvalStatus])
  @@index([location])
  @@index([employmentType])
  @@index([workType])
  @@index([experienceLevel])
  @@index([expiryDate])
}

model JobPostingAnalytics {
  id                  String              @id @default(auto()) @map("_id") @db.ObjectId
  jobPostingId        String              @unique @db.ObjectId
  jobPosting          JobPosting          @relation(fields: [jobPostingId], references: [id])
  startDate           DateTime
  endDate             DateTime?
  views               Int                 @default(0)
  uniqueViews         Int                 @default(0)
  viewsByDate         Json?
  averageViewDuration Float?
  bounceRate          Float?
  applications        Int                 @default(0)
  applicationsByDate  Json?
  conversionRate      Float?
  applicationQualityScore Float?
  applicationsBySource Json?
  viewerDemographics   Json?
  applicantDemographics Json?
  locationData         Json?
  deviceStats          Json?

  @@index([startDate, endDate])
}

model Application {
  id                 String               @id @default(auto()) @map("_id") @db.ObjectId
  jobSeekerId        String               @db.ObjectId
  jobSeeker          JobSeeker            @relation(fields: [jobSeekerId], references: [id])
  jobPostingId       String               @db.ObjectId
  jobPosting         JobPosting           @relation(fields: [jobPostingId], references: [id])
  resumeId           String               @db.ObjectId
  resume             Resume               @relation(fields: [resumeId], references: [id])
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
  coverLetter        String?
  typedCoverLetter   String?
  additionalDocuments Document[]
  screeningAnswers   ApplicationAnswer[]
  hiringStage        String               @default("Applied") // Applied, Shortlisted, Interviewing, Offered, Hired, Rejected
  status             String
  lastStatusUpdate   DateTime?
  rejectionReason    String?
  rating             Int?
  comments           ApplicationComment[]
  ratings            ApplicationRating[]
  interviewNotes     String?
  communications     CommunicationLog[]

  @@index([jobSeekerId])
  @@index([jobPostingId])
  @@index([resumeId])
  @@index([hiringStage])
  @@index([status])
  @@index([createdAt])
}

model Resume {
  id                 String           @id @default(auto()) @map("_id") @db.ObjectId
  jobSeekerId        String           @db.ObjectId
  jobSeeker          JobSeeker        @relation(fields: [jobSeekerId], references: [id])
  title              String?
  createdAt          DateTime         @default(now())
  updatedAt          DateTime         @updatedAt
  isActive           Boolean          @default(false)
  requiresModeration Boolean          @default(false)
  moderationStatus   String?          // Pending, Approved, Rejected
  summary            String?
  education          Education[]
  workExperience     WorkExperience[]
  skills             Skill[]
  certifications     String[]
  languages          String[]
  achievements       String[]
  applications       Application[]

  @@index([jobSeekerId])
  @@index([isActive])
}

model Education {
  id                  String    @id @default(auto()) @map("_id") @db.ObjectId
  resumeId            String    @db.ObjectId
  resume              Resume    @relation(fields: [resumeId], references: [id])
  institution         String
  location            String?
  degree              String
  fieldOfStudy        String
  startDate           DateTime
  endDate             DateTime?
  isCurrentlyEnrolled Boolean   @default(false)
  grade               String?
  activities          String[]
  achievements        String[]
  description         String?
  createdAt           DateTime  @default(now())
  updatedAt           DateTime  @updatedAt

  @@index([resumeId])
  @@index([institution])
  @@index([degree])
  @@index([fieldOfStudy])
}

model WorkExperience {
  id               String    @id @default(auto()) @map("_id") @db.ObjectId
  resumeId         String    @db.ObjectId
  resume           Resume    @relation(fields: [resumeId], references: [id])
  companyName      String
  location         String
  jobTitle         String
  department       String?
  startDate        DateTime
  endDate          DateTime?
  isCurrentJob     Boolean   @default(false)
  employmentType   String?
  responsibilities String[]
  achievements     String[]
  skills           String[]
  projects         String[]
  description      String?
  toolsUsed        String[]
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt

  @@index([resumeId])
  @@index([companyName])
  @@index([jobTitle])
  @@fulltext([companyName, jobTitle, description])
}

model Skill {
  id              String    @id @default(auto()) @map("_id") @db.ObjectId
  resumeId        String    @db.ObjectId
  resume          Resume    @relation(fields: [resumeId], references: [id])
  name            String
  category        String?   // Technical, Soft Skill, Language, etc.
  proficiency     String?   // Beginner, Intermediate, Expert
  yearsOfExperience Int?
  certifications  String[]
  lastUsed        DateTime?
  endorsements    Int?
  description     String?
  isHighlighted   Boolean   @default(false)

  @@index([resumeId])
  @@index([name])
  @@index([category])
  @@index([proficiency])
}

model ApplicationAnswer {
  id             String    @id @default(auto()) @map("_id") @db.ObjectId
  applicationId  String    @db.ObjectId
  application    Application @relation(fields: [applicationId], references: [id])
  questionId     String    @db.ObjectId
  question       ScreeningQuestion @relation(fields: [questionId], references: [id])
  answerText     String
  submittedAt    DateTime  @default(now())
  score          Int?
  isCorrect      Boolean?
  evaluatorNotes String?
  flags          String[]
  status         String?

  @@index([applicationId])
  @@index([questionId])
}

model ScreeningQuestion {
  id             String              @id @default(auto()) @map("_id") @db.ObjectId
  jobPostingId   String              @db.ObjectId
  jobPosting     JobPosting          @relation(fields: [jobPostingId], references: [id])
  questionText   String
  questionType   String              // e.g., text, multiple-choice, yes/no
  required       Boolean             @default(false)
  order          Int
  options        String[]
  correctAnswer  String?
  scoreWeight    Int?
  maxLength      Int?
  hint           String?
  applicationAnswers ApplicationAnswer[]

  @@index([jobPostingId])
}

model ApplicationComment {
  id            String    @id @default(auto()) @map("_id") @db.ObjectId
  applicationId String    @db.ObjectId
  application   Application @relation(fields: [applicationId], references: [id])
  commenterId   String    @db.ObjectId
  commenter     User      @relation(fields: [commenterId], references: [id])
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  comment       String
  visibility    String    // e.g., team, private, all
  category      String?   // e.g., feedback, interview notes, general
  priority      String?   // e.g., high, medium, low
  tags          String[]

  @@index([applicationId])
  @@index([commenterId])
}

model ApplicationRating {
  id            String      @id @default(auto()) @map("_id") @db.ObjectId
  applicationId String      @db.ObjectId
  application   Application @relation(fields: [applicationId], references: [id])
  raterId       String      @db.ObjectId
  rater         User        @relation(fields: [raterId], references: [id])
  rating        Int         // 1-5 scale
  category      String      // skills, communication, overall, etc.
  comment       String?
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  @@index([applicationId])
  @@index([raterId])
}

model Document {
  id        String @id @default(auto()) @map("_id") @db.ObjectId
  name      String
  url       String
  createdAt DateTime @default(now())
  application Application @relation(fields: [applicationId], references: [id])
  applicationId String @db.ObjectId

  @@index([applicationId])
}

model CompanyReview {
  id                  String    @id @default(auto()) @map("_id") @db.ObjectId
  companyId           String    @db.ObjectId
  company             Employer   @relation(fields: [companyId], references: [id])
  reviewerId          String    @db.ObjectId
  reviewer            User      @relation(fields: [reviewerId], references: [id])
  createdAt           DateTime  @default(now())
  updatedAt           DateTime  @updatedAt
  rating              Int
  title               String
  content             String
  pros                String
  cons                String
  advice              String?
  workLifeBalance     Int?
  compensation        Int?
  careerGrowth        Int?
  management          Int?
  culture             Int?
  status              String    // pending, approved, rejected
  isVerified          Boolean   @default(false)
  isAnonymous         Boolean   @default(false)
  helpfulVotes        Int       @default(0)
  reportCount         Int       @default(0)
  companyResponse     CompanyResponse?

  @@index([companyId])
  @@index([reviewerId])
  @@index([rating])
  @@fulltext([title, content, pros, cons])
}

model CompanyResponse {
  id               String    @id @default(auto()) @map("_id") @db.ObjectId
  reviewId         String    @unique @db.ObjectId
  review           CompanyReview @relation(fields: [reviewId], references: [id])
  responderId      String    @db.ObjectId
  responder        User      @relation(fields: [responderId], references: [id])
  companyId        String    @db.ObjectId
  company          Employer @relation(fields: [companyId], references: [id])
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
  content          String
  position         String
  department       String
  isOfficial       Boolean   @default(false)
  isPublic         Boolean   @default(true)
  status           String?
  moderationStatus String?
  lastModifiedBy   String?   @db.ObjectId
  version          Int       @default(1)
  isPinned         Boolean   @default(false)

  @@index([responderId])
}

model JobAlert {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  userId            String   @db.ObjectId
  user              User     @relation(fields: [userId], references: [id])
  name              String
  createdAt         DateTime @default(now())
  status            String
  keywords          String[]
  locations         String[]
  jobTypes          String[]
  industries        String[]
  salaryRange       Range?
  experienceLevels  String[]
  remotePreference  String?
  companyTypes      String[]
  frequency         String   // daily, weekly
  emailNotification Boolean  @default(true)
  pushNotification  Boolean  @default(false)
  lastSentAt        DateTime?
  matchCount        Int      @default(0)

  @@index([userId])
  @@index([status])
  @@index([frequency])
}

model JobSearch {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  jobSeekerId String   @db.ObjectId
  user        User     @relation(fields: [jobSeekerId], references: [id])
  keywords    String?
  filters     String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([jobSeekerId])
}

model JobSearchHistory {
  id               String    @id @default(auto()) @map("_id") @db.ObjectId
  userId           String    @db.ObjectId
  user             User      @relation(fields: [userId], references: [id])
  searchTimestamp  DateTime  @default(now())
  sessionId        String?
  searchType       String?
  keywords         String?
  location         String?
  jobType          String[]
  salaryRange      Range?
  experienceLevel  String?
  remotePreference String?
  resultCount      Int?
  viewedJobs       Int[]
  appliedJobs      Int[]
  savedJobs        Int[]
  relevanceScore   Float?

  @@index([userId])
  @@index([searchTimestamp])
  @@index([sessionId])
}

model CommunicationLog {
  id               String    @id @default(auto()) @map("_id") @db.ObjectId
  applicationId    String    @db.ObjectId
  application      Application @relation(fields: [applicationId], references: [id])
  senderId         String    @db.ObjectId
  sender           User      @relation("Sender", fields: [senderId], references: [id])
  recipientId      String    @db.ObjectId
  recipient        User      @relation("Recipient", fields: [recipientId], references: [id])
  timestamp        DateTime  @default(now())
  messageType      String    // email, in-app, interview invite
  subject          String?
  content          String
  status           String    // sent, delivered, read
  attachments      String[]
  interviewDetails Json?
  scheduledTime    DateTime?
  location         String?
  interviewType    String?

  @@index([applicationId])
  @@index([senderId])
  @@index([recipientId])
  @@index([timestamp])
}

model Notification {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  userId    String   @db.ObjectId
  user      User     @relation(fields: [userId], references: [id])
  type      String
  message   String
  status    String   @default("unread") // read, unread
  createdAt DateTime @default(now())

  @@index([userId])
  @@index([type])
  @@index([status])
}

model Role {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  permissions String[]
  level       Int      @default(0)
  isSystem    Boolean  @default(false)
  isActive    Boolean  @default(true)
  users       User[]

  @@index([name])
  @@index([level])
  @@index([isSystem])
  @@index([isActive])
}

model ModerationItem {
  id                    String    @id @default(auto()) @map("_id") @db.ObjectId
  contentType           String    // JobPosting, Resume, etc.
  contentId             String    @db.ObjectId
  moderatorId           String    @db.ObjectId
  moderator             User      @relation(fields: [moderatorId], references: [id])
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
  status                String    // Pending, Approved, Rejected
  priority              String    // High, Medium, Low
  reason                String?
  notes                 String?
  decision              String?
  decisionReason        String?
  hasInappropriateContent Boolean   @default(false)
  requiresManualReview  Boolean   @default(false)
  isAutoModerated       Boolean   @default(false)

  @@index([contentType, contentId])
  @@index([moderatorId])
  @@index([status])
  @@index([priority])
}

model Invoice {
  id            String        @id @default(auto()) @map("_id") @db.ObjectId
  invoiceNumber String
  payerId       String        @db.ObjectId
  payer         User          @relation(fields: [payerId], references: [id])
  createdAt     DateTime      @default(now())
  dueDate       DateTime
  items         InvoiceItem[]
  subtotal      Float
  tax           Float
  fees          Float
  total         Float
  currency      String
  status        String        // e.g., draft, pending, paid, overdue, void
  paidAt        DateTime?
  paymentMethod String?
  transactionId String?     @unique @db.ObjectId
  transaction   Transaction? @relation(fields: [transactionId], references: [id])
  notes         String?

  @@index([payerId])
  @@index([invoiceNumber])
  @@index([status])
}

model InvoiceItem {
  id             String   @id @default(auto()) @map("_id") @db.ObjectId
  invoiceId      String   @db.ObjectId
  invoice        Invoice  @relation(fields: [invoiceId], references: [id])
  description    String
  createdAt      DateTime @default(now())
  quantity       Int
  unitPrice      Float
  amount         Float
  taxRate        Float?
  taxAmount      Float?
  discountAmount Float?
  productId      String?
  productType    String?
  sku            String?
  metadata       Json?

  @@index([invoiceId])
}

model Transaction {
  id             String    @id @default(auto()) @map("_id") @db.ObjectId
  payerId        String    @db.ObjectId
  payer          User      @relation("TransactionPayer", fields: [payerId], references: [id])
  receiverId     String    @db.ObjectId
  receiver       User      @relation("TransactionReceiver", fields: [receiverId], references: [id])
  createdAt      DateTime  @default(now())
  completedAt    DateTime?
  amount         Float
  currency       String
  paymentMethod  String
  paymentGatewayId String?
  status         String // Pending, Completed, Failed
  type           String
  description    String?
  metadata       Json?
  refundStatus   String?
  failureReason  String?
  invoice        Invoice?
  receipt        Receipt?

  @@index([payerId])
  @@index([receiverId])
  @@index([status])
  @@index([type])
}

model Receipt {
  id             String    @id @default(auto()) @map("_id") @db.ObjectId
  receiptNumber  String    @unique
  transactionId  String    @unique @db.ObjectId
  transaction    Transaction @relation(fields: [transactionId], references: [id])
  issueDate      DateTime  @default(now())
  payerId        String    @db.ObjectId
  payer          User      @relation(fields: [payerId], references: [id])
  amount         Float
  currency       String
  paymentMethod  String
  paymentReference String?
  description    String?
  items          ReceiptItem[]
  taxDetails     Json?
  metadata       Json?
  status         String?
  notes          String?

  @@index([payerId])
}

model ReceiptItem {
  id             String    @id @default(auto()) @map("_id") @db.ObjectId
  receiptId      String    @db.ObjectId
  receipt        Receipt   @relation(fields: [receiptId], references: [id])
  description    String
  createdAt      DateTime  @default(now())
  quantity       Int
  unitPrice      Float
  amount         Float
  taxAmount      Float?
  discountAmount Float?
  finalAmount    Float
  serviceId      String?
  serviceType    String?
  periodStart    DateTime?
  periodEnd      DateTime?

  @@index([receiptId])
}

model Reminder {
  id                 String    @id @default(auto()) @map("_id") @db.ObjectId
  userId             String    @db.ObjectId
  user               User      @relation(fields: [userId], references: [id])
  type               String
  createdAt          DateTime  @default(now())
  targetDate         DateTime
  title              String
  description        String?
  priority           String?
  status             String    @default("pending") // pending, completed, snoozed
  recurrence         String?
  notificationMethod String?
  notificationTime   DateTime?
  isNotified         Boolean   @default(false)
  snoozeCount        Int       @default(0)
  snoozeInterval     String?

  @@index([userId])
  @@index([type])
  @@index([targetDate])
  @@index([status])
}

model SavedJob {
  id             String    @id @default(auto()) @map("_id") @db.ObjectId
  userId         String    @db.ObjectId
  user           User      @relation(fields: [userId], references: [id])
  jobId          String    @db.ObjectId
  job            JobPosting @relation(fields: [jobId], references: [id])
  savedAt        DateTime  @default(now())
  expiresAt      DateTime?
  status         String?
  isNotified     Boolean   @default(false)
  isApplied      Boolean   @default(false)
  isHidden       Boolean   @default(false)
  notes          String?
  reminderEnabled Boolean   @default(false)
  reminderDate   DateTime?
  reminderType   String?
  reminderSent   Boolean   @default(false)
  customTags     String[]

  @@index([userId])
  @@index([jobId])
  @@index([savedAt])
}

model ProfileVisibility {
  id                 String   @id @default(auto()) @map("_id") @db.ObjectId
  jobSeekerId        String   @db.ObjectId
  jobSeeker          JobSeeker @relation(fields: [jobSeekerId], references: [id])
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  overallVisibility  String   @default("private") // public, private, connections
  searchVisibility   Boolean  @default(true)
  employerVisibility Boolean  @default(true)
  networkVisibility  Boolean  @default(false)
  anonymousMode      Boolean  @default(false)
  sectionSettings    Json?
  customSections     Json?
  excludedCompanies  Int[]
  allowedCompanies   Int[]
  restrictedFields   String[]

  @@index([jobSeekerId])
}

model SystemConfiguration {
  id             String   @id @default(auto()) @map("_id") @db.ObjectId
  key            String   @unique
  value          String
  type           String   // string, number, boolean, json
  category       String
  description    String?
  defaultValue   String?
  isEncrypted    Boolean  @default(false)
  lastModified   DateTime @default(now())
  modifiedBy     String   @db.ObjectId
  validationRules Json?
  allowedValues  String[]
  minValue       Float?
  maxValue       Float?
  pattern        String?

  @@index([category])
  @@index([type])
}

model UserActivityLog {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  userId       String   @db.ObjectId
  user         User     @relation(fields: [userId], references: [id])
  sessionId    String?
  timestamp    DateTime @default(now())
  ipAddress    String
  activityType String
  page         String?
  action       String
  target       String?
  result       String?
  deviceInfo   Json?
  browserInfo  String?
  location     String?
  referrer     String?
  duration     Int?

  @@index([userId])
  @@index([timestamp])
  @@index([activityType])
  @@index([action])
}

model NotificationTemplate {
  id               String   @id @default(auto()) @map("_id") @db.ObjectId
  name             String   @unique
  type             String   // email, sms, in-app
  category         String
  subject          String?
  content          String
  variables        String[]
  isActive         Boolean  @default(true)
  metadata         Json?

  @@index([type])
  @@index([category])
}

model SupportTicket {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  userId            String   @db.ObjectId
  user              User     @relation(fields: [userId], references: [id])
  issueDescription  String
  status            String   @default("open")
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  assignedAdminId   String?  @db.ObjectId

  @@index([userId])
  @@index([status])
  @@index([createdAt])
}

model AuditLog {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  timestamp   DateTime @default(now())
  userId      String   @db.ObjectId
  user        User     @relation(fields: [userId], references: [id])
  ipAddress   String
  userAgent   String?
  action      String
  entityType  String
  entityId    String   @db.ObjectId
  status      String
  description String
  previousState Json?
  newState      Json?
  changes       String[]
  reason        String?
  metadata      Json?

  @@index([userId])
  @@index([timestamp])
  @@index([action])
  @@index([entityType, entityId])
  @@index([status])
}

model ApiKey {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  userId      String   @db.ObjectId
  user        User     @relation(fields: [userId], references: [id])
  name        String
  key         String   @unique
  prefix      String
  scopes      String[]
  status      String   @default("active") // active, revoked, expired
  type        String   // test, production
  rateLimit   Json?
  ipWhitelist String[]
  lastUsed    DateTime?
  expiresAt   DateTime?
  metadata    Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([userId])
  @@index([status])
}

type Range {
  min      Int
  max      Int
  currency String?
}

type SalaryRange {
  min      Int
  max      Int
  currency String
  period   String? // hourly, monthly, yearly
}

model SystemSetting {
  id             String   @id @default(auto()) @map("_id") @db.ObjectId
  key            String   @unique
  value          String
  type           String   // string, number, boolean, json
  category       String
  description    String?
  defaultValue   String?
  isEncrypted    Boolean  @default(false)
  lastModified   DateTime @default(now())
  modifiedBy     String   @db.ObjectId
  validationRules Json?
  allowedValues  String[]
  minValue       Float?
  maxValue       Float?
  pattern        String?

  @@index([category])
  @@index([type])
}
