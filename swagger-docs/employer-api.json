{"openapi": "3.0.0", "info": {"title": "Employer API", "version": "1.0.0", "description": "API for managing employer functionalities, including registration, profile management, and job postings."}, "paths": {"/api/employers/register": {"post": {"summary": "Register an employer account", "description": "Register a new employer account with company details.", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"companyName": {"type": "string"}, "email": {"type": "string", "format": "email"}, "password": {"type": "string"}, "address": {"type": "string"}, "industry": {"type": "string"}}, "required": ["companyName", "email", "password"]}}}}, "responses": {"200": {"description": "Employer registered successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"employerId": {"type": "string"}, "message": {"type": "string"}}}, "example": {"employerId": "employer123", "message": "Employer registered successfully."}}}}}}}, "/api/employers/profile": {"get": {"summary": "Retrieve employer profile", "description": "Get the profile details of the employer.", "responses": {"200": {"description": "Employer profile retrieved", "content": {"application/json": {"schema": {"type": "object", "properties": {"profile": {"type": "object", "properties": {"companyName": {"type": "string"}, "email": {"type": "string", "format": "email"}, "address": {"type": "string"}, "industry": {"type": "string"}, "socialLinks": {"type": "object"}}}}}, "example": {"profile": {"companyName": "Tech Innovators Inc.", "email": "<EMAIL>", "address": "123 Tech Lane, Silicon Valley, CA", "industry": "Information Technology", "socialLinks": {"linkedin": "https://linkedin.com/company/tech-innovators"}}}}}}}}, "put": {"summary": "Update employer profile", "description": "Update the profile details of the employer.", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"companyDetails": {"type": "object", "properties": {"companyName": {"type": "string"}, "address": {"type": "string"}, "industry": {"type": "string"}}}, "socialLinks": {"type": "object", "properties": {"linkedin": {"type": "string"}, "twitter": {"type": "string"}}}}}}}}, "responses": {"200": {"description": "Profile updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}, "example": {"message": "Profile updated successfully."}}}}}}}, "/api/employers/jobs": {"get": {"summary": "List job postings", "description": "Retrieve a list of job postings created by the employer.", "parameters": [{"name": "status", "in": "query", "required": false, "schema": {"type": "string", "enum": ["active", "paused", "closed"]}}], "responses": {"200": {"description": "List of job postings", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"jobId": {"type": "string"}, "title": {"type": "string"}, "status": {"type": "string"}}}}, "example": [{"jobId": "job123", "title": "Software Engineer", "status": "active"}]}}}}}, "post": {"summary": "Create a job posting", "description": "Create a new job posting with detailed information.", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "requirements": {"type": "array", "items": {"type": "string"}}, "salaryRange": {"type": "string"}, "location": {"type": "string"}}, "required": ["title", "description", "requirements"]}}}}, "responses": {"200": {"description": "Job created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"jobId": {"type": "string"}, "message": {"type": "string"}}}, "example": {"jobId": "job123", "message": "Job created successfully."}}}}}}}, "/api/employers/jobs/{id}": {"put": {"summary": "Update a job posting", "description": "Update the details of an existing job posting.", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"updates": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "requirements": {"type": "array", "items": {"type": "string"}}, "salaryRange": {"type": "string"}, "location": {"type": "string"}}}}}}}}, "responses": {"200": {"description": "Job updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}, "example": {"message": "Job updated successfully."}}}}}}, "delete": {"summary": "Delete a job posting", "description": "Delete a specific job posting by its ID.", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Job deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}, "example": {"message": "Job deleted successfully."}}}}}}}}}