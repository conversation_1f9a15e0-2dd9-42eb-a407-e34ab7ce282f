{"openapi": "3.0.0", "info": {"title": "Administrator API", "version": "1.0.0", "description": "API for managing administrator functionalities, including user management, content moderation, system settings, analytics, and support management."}, "paths": {"/api/admin/users": {"get": {"summary": "List user accounts", "description": "Retrieve a list of user accounts with filtering options.", "parameters": [{"name": "role", "in": "query", "required": false, "schema": {"type": "string", "enum": ["seeker", "employer", "admin"]}}, {"name": "status", "in": "query", "required": false, "schema": {"type": "string", "enum": ["active", "inactive", "suspended"]}}], "responses": {"200": {"description": "List of user accounts", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"userId": {"type": "string"}, "email": {"type": "string"}, "role": {"type": "string"}, "status": {"type": "string"}}}}, "example": [{"userId": "user123", "email": "<EMAIL>", "role": "seeker", "status": "active"}]}}}}}, "post": {"summary": "Create a user account", "description": "Create a new user account with specified details.", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "role": {"type": "string", "enum": ["seeker", "employer", "admin"]}, "status": {"type": "string", "enum": ["active", "inactive"]}}, "required": ["email", "role"]}}}}, "responses": {"201": {"description": "User account created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"userId": {"type": "string"}, "message": {"type": "string"}}}, "example": {"userId": "user123", "message": "User account created successfully."}}}}}}, "patch": {"summary": "Update user account", "description": "Update details of a user account.", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"userId": {"type": "string"}, "status": {"type": "string", "enum": ["active", "inactive", "suspended"]}}, "required": ["userId"]}}}}, "responses": {"200": {"description": "User account updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}, "example": {"message": "User account updated successfully."}}}}}}, "delete": {"summary": "Delete a user account", "description": "Delete a user account by ID.", "parameters": [{"name": "userId", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "User account deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}, "example": {"message": "User account deleted successfully."}}}}}}}, "/api/admin/content": {"get": {"summary": "List content for moderation", "description": "Retrieve content for moderation with filtering options.", "parameters": [{"name": "type", "in": "query", "required": false, "schema": {"type": "string", "enum": ["resume", "jobPosting", "review"]}}, {"name": "status", "in": "query", "required": false, "schema": {"type": "string", "enum": ["pending", "approved", "rejected"]}}], "responses": {"200": {"description": "List of content for moderation", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"contentId": {"type": "string"}, "type": {"type": "string"}, "status": {"type": "string"}}}}, "example": [{"contentId": "content123", "type": "resume", "status": "pending"}]}}}}}, "post": {"summary": "Create content entry", "description": "Create a new content entry for moderation.", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"type": {"type": "string", "enum": ["resume", "jobPosting", "review"]}, "status": {"type": "string", "enum": ["pending", "approved", "rejected"]}}, "required": ["type"]}}}}, "responses": {"201": {"description": "Content entry created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"contentId": {"type": "string"}, "message": {"type": "string"}}}, "example": {"contentId": "content123", "message": "Content entry created successfully."}}}}}}, "patch": {"summary": "Moderate content", "description": "Approve or reject user-generated content.", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"contentId": {"type": "string"}, "action": {"type": "string", "enum": ["approve", "reject"]}}, "required": ["contentId", "action"]}}}}, "responses": {"200": {"description": "Content moderation action completed", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}, "example": {"message": "Content approved successfully."}}}}}}, "delete": {"summary": "Delete content entry", "description": "Delete a content entry by ID.", "parameters": [{"name": "contentId", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Content entry deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}, "example": {"message": "Content entry deleted successfully."}}}}}}}, "/api/admin/system-settings": {"get": {"summary": "View system settings", "description": "Retrieve current system settings.", "responses": {"200": {"description": "System settings retrieved", "content": {"application/json": {"schema": {"type": "object", "properties": {"settings": {"type": "object", "properties": {"emailSettings": {"type": "object"}, "paymentGateways": {"type": "object"}}}}}, "example": {"settings": {"emailSettings": {"smtpServer": "smtp.example.com", "port": 587, "useSSL": true}, "paymentGateways": {"gatewayName": "Stripe", "apiKey": "sk_test_4eC39HqLyjWDarjtT1zdp7dc"}}}}}}}}, "put": {"summary": "Update system settings", "description": "Configure system-wide settings such as email and payment gateways.", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"emailSettings": {"type": "object", "properties": {"smtpServer": {"type": "string"}, "port": {"type": "integer"}, "useSSL": {"type": "boolean"}}}, "paymentGateways": {"type": "object", "properties": {"gatewayName": {"type": "string"}, "apiKey": {"type": "string"}}}}}}}}, "responses": {"200": {"description": "Settings updated", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}, "example": {"message": "System settings updated successfully."}}}}}}}, "/api/admin/analytics": {"get": {"summary": "View system analytics", "description": "Access system usage and performance metrics.", "responses": {"200": {"description": "Analytics data retrieved", "content": {"application/json": {"schema": {"type": "object", "properties": {"analytics": {"type": "object", "properties": {"userMetrics": {"type": "object"}, "jobMetrics": {"type": "object"}}}}}, "example": {"analytics": {"userMetrics": {"totalUsers": 1000, "activeUsers": 800}, "jobMetrics": {"totalJobs": 500, "activeJobs": 450}}}}}}}}}, "/api/admin/support-tickets": {"get": {"summary": "View support tickets", "description": "Retrieve a list of support tickets with filtering options.", "parameters": [{"name": "status", "in": "query", "required": false, "schema": {"type": "string", "enum": ["open", "closed", "pending"]}}], "responses": {"200": {"description": "List of support tickets", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"ticketId": {"type": "string"}, "subject": {"type": "string"}, "status": {"type": "string"}}}}, "example": [{"ticketId": "ticket123", "subject": "Login issue", "status": "open"}]}}}}}, "patch": {"summary": "Update support ticket status", "description": "Change the status of a support ticket.", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"ticketId": {"type": "string"}, "status": {"type": "string", "enum": ["open", "closed", "pending"]}}, "required": ["ticketId", "status"]}}}}, "responses": {"200": {"description": "Support ticket status updated", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}, "example": {"message": "Support ticket status updated to closed."}}}}}}}}}