{"openapi": "3.0.0", "info": {"title": "Job Seeker API", "version": "1.0.0", "description": "API for managing job seeker functionalities, including user registration, login, profile updates, resume management, job search, and applications."}, "paths": {"/api/auth/register": {"post": {"summary": "Register a new user", "description": "Register using email, mobile, or social media.", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string"}, "mobile": {"type": "string"}, "socialMedia": {"type": "object", "properties": {"platform": {"type": "string"}, "token": {"type": "string"}}}}, "required": ["email", "password"]}}}}, "responses": {"200": {"description": "User registered successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"userId": {"type": "string"}, "message": {"type": "string"}}}, "example": {"userId": "user123", "message": "User registered successfully."}}}}}}}, "/api/auth/login": {"post": {"summary": "Login a user", "description": "Login using email/password, mobile/OTP, or social media.", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "oneOf": [{"properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string"}}, "required": ["email", "password"]}, {"properties": {"mobile": {"type": "string"}, "otp": {"type": "string"}}, "required": ["mobile", "otp"]}, {"properties": {"socialMedia": {"type": "object", "properties": {"platform": {"type": "string"}, "token": {"type": "string"}}}}, "required": ["socialMedia"]}]}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"token": {"type": "string"}, "message": {"type": "string"}}}, "example": {"token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "message": "Login successful."}}}}}}}, "/api/auth/logout": {"post": {"summary": "Logout the user", "description": "Logout the user by invalidating the session token.", "parameters": [{"name": "Authorization", "in": "header", "required": true, "schema": {"type": "string"}, "description": "Bearer token for authentication"}], "responses": {"200": {"description": "User logged out successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}, "example": {"message": "User logged out successfully."}}}}}}}, "/api/auth/reset-password": {"post": {"summary": "Request password reset", "description": "Request password reset via email or mobile.", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "oneOf": [{"properties": {"email": {"type": "string", "format": "email"}}, "required": ["email"]}, {"properties": {"mobile": {"type": "string"}}, "required": ["mobile"]}]}}}}, "responses": {"200": {"description": "Password reset link/OTP sent", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}, "example": {"message": "Password reset link/OTP sent."}}}}}}}, "/api/user/profile": {"get": {"summary": "Retrieve user profile", "description": "Get the user's profile information.", "responses": {"200": {"description": "User profile retrieved", "content": {"application/json": {"schema": {"type": "object", "properties": {"profile": {"type": "object", "properties": {"name": {"type": "string"}, "dob": {"type": "string", "format": "date"}, "nationality": {"type": "string"}, "contact": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "mobile": {"type": "string"}}}, "preferences": {"type": "object", "properties": {"jobType": {"type": "string"}, "location": {"type": "string"}}}}}}}, "example": {"profile": {"name": "<PERSON>", "dob": "1990-01-01", "nationality": "American", "contact": {"email": "<EMAIL>", "mobile": "+1234567890"}, "preferences": {"jobType": "Full-time", "location": "Remote"}}}}}}}}, "put": {"summary": "Update user profile", "description": "Update the user's profile information.", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "dob": {"type": "string", "format": "date"}, "nationality": {"type": "string"}, "contact": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "mobile": {"type": "string"}}}, "preferences": {"type": "object", "properties": {"jobType": {"type": "string"}, "location": {"type": "string"}}}}}}}}, "responses": {"200": {"description": "Profile updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}, "example": {"message": "Profile updated successfully."}}}}}}}, "/api/user/visibility": {"patch": {"summary": "Update profile visibility", "description": "Update the visibility settings of the user's profile.", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"visibility": {"type": "string", "enum": ["public", "private", "partially_visible"]}, "sections": {"type": "array", "items": {"type": "string"}}}}}}}, "responses": {"200": {"description": "Profile visibility updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}, "example": {"message": "Profile visibility updated successfully."}}}}}}}, "/api/resumes": {"get": {"summary": "Get all resumes", "description": "Retrieve all resumes uploaded by the user.", "responses": {"200": {"description": "List of resumes", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"resumeId": {"type": "string"}, "fileName": {"type": "string"}, "uploadedDate": {"type": "string", "format": "date"}}}}, "example": [{"resumeId": "resume123", "fileName": "resume.pdf", "uploadedDate": "2024-01-01"}]}}}}}, "post": {"summary": "Upload a new resume", "description": "Upload a new resume in PDF, DOC, or DOCX format.", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "Resume uploaded successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"resumeId": {"type": "string"}, "message": {"type": "string"}}}, "example": {"resumeId": "resume123", "message": "Resume uploaded successfully."}}}}}}}, "/api/resumes/{id}": {"delete": {"summary": "Delete a resume", "description": "Delete a specific resume by its ID.", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Resume deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}, "example": {"message": "Resume deleted successfully."}}}}}}}, "/api/resumes/build": {"post": {"summary": "Create a resume", "description": "Create a resume using the resume builder tool.", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"personalDetails": {"type": "object"}, "education": {"type": "array", "items": {"type": "object"}}, "workExperience": {"type": "array", "items": {"type": "object"}}, "skills": {"type": "array", "items": {"type": "string"}}}}}}}, "responses": {"200": {"description": "Resume created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"resumeId": {"type": "string"}, "message": {"type": "string"}}}, "example": {"resumeId": "resume123", "message": "Resume created successfully."}}}}}}}, "/api/jobs": {"get": {"summary": "Search for jobs", "description": "Search for jobs with filters such as keyword, location, category, etc.", "parameters": [{"name": "keywords", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "location", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "category", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "salaryRange", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "postedDate", "in": "query", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "jobType", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "experienceLevel", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "List of jobs matching search criteria", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"jobId": {"type": "string"}, "title": {"type": "string"}, "company": {"type": "string"}, "location": {"type": "string"}, "salary": {"type": "string"}}}}, "example": [{"jobId": "job123", "title": "Software Engineer", "company": "Tech Innovators Inc.", "location": "Remote", "salary": "$70,000 - $90,000"}]}}}}}}, "/api/jobs/{id}": {"get": {"summary": "Get job details", "description": "Get details of a specific job posting by its ID.", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Job details retrieved", "content": {"application/json": {"schema": {"type": "object", "properties": {"job": {"type": "object", "properties": {"jobId": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "requirements": {"type": "string"}, "salaryRange": {"type": "string"}, "location": {"type": "string"}}}}}, "example": {"job": {"jobId": "job123", "title": "Software Engineer", "description": "Develop and maintain software applications.", "requirements": "3+ years of experience in software development.", "salaryRange": "$70,000 - $90,000", "location": "Remote"}}}}}}}}, "/api/alerts": {"get": {"summary": "Get job alerts", "description": "Retrieve all job alerts created by the user.", "responses": {"200": {"description": "List of job alerts", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"alertId": {"type": "string"}, "name": {"type": "string"}, "criteria": {"type": "object"}}}}, "example": [{"alertId": "alert123", "name": "Software Jobs", "criteria": {"keywords": "Software Engineer", "location": "Remote", "salary": "70000-90000", "jobType": "Full-time", "frequency": "daily"}}]}}}}}, "post": {"summary": "Create a job alert", "description": "Create a new job alert based on search criteria.", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "criteria": {"type": "object", "properties": {"keywords": {"type": "string"}, "location": {"type": "string"}, "salary": {"type": "string"}, "jobType": {"type": "string"}, "frequency": {"type": "string", "enum": ["daily", "weekly"]}}}}}}}}, "responses": {"200": {"description": "<PERSON><PERSON> created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"alertId": {"type": "string"}, "message": {"type": "string"}}}, "example": {"alertId": "alert123", "message": "<PERSON><PERSON> created successfully."}}}}}}}, "/api/alerts/{id}": {"delete": {"summary": "Delete a job alert", "description": "Delete a specific job alert by its ID.", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Job alert deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}, "example": {"message": "Job alert deleted successfully."}}}}}}}, "/api/applications": {"post": {"summary": "Apply for a job", "description": "Submit a job application with resume and cover letter.", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"jobId": {"type": "string"}, "resumeId": {"type": "string"}, "coverLetter": {"type": "string"}, "preScreenAnswers": {"type": "object"}}}}}}, "responses": {"200": {"description": "Application submitted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"applicationId": {"type": "string"}, "message": {"type": "string"}}}, "example": {"applicationId": "app123", "message": "Application submitted successfully."}}}}}}}, "/api/applications/{id}": {"get": {"summary": "View application status", "description": "View the status of a specific job application.", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Application status retrieved", "content": {"application/json": {"schema": {"type": "object", "properties": {"application": {"type": "object", "properties": {"applicationId": {"type": "string"}, "status": {"type": "string"}, "jobId": {"type": "string"}, "resumeId": {"type": "string"}}}}}, "example": {"application": {"applicationId": "app123", "status": "pending", "jobId": "job123", "resumeId": "resume123"}}}}}}}}}}