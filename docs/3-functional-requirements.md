# 3. Functional Requirements

## 3.1 Job Seeker Functionality (Expanded)

*   **3.1.1 User Account Management**
    *   **FR-JS-ACC-001:** User Registration
        *   **Description:** Users should be able to register for a new account using their email address, mobile number, or social media accounts (Google, LinkedIn, Facebook).
        *   **Use Cases:**
            *   UC-JS-REG-01: Register using email address and password. The system should send a verification email to the provided address.
            *   UC-JS-REG-02: Register using a mobile number and receive a verification code via SMS.
            *   UC-JS-REG-03: Register using a social media account (Google, LinkedIn, Facebook). The system should retrieve relevant profile information from the social media platform.
            *   UC-JS-REG-04: Handle registration errors, such as duplicate email/mobile number, weak password, etc., with appropriate error messages.
    *   **FR-JS-ACC-002:** User Login
        *   **Description:** Users should be able to log in securely using their registered credentials.
        *   **Use Cases:**
            *   UC-JS-LOGIN-01: Log in using email address and password.
            *   UC-JS-LOGIN-02: Log in using mobile number and password/OTP.
            *   UC-JS-LOGIN-03: Log in using a linked social media account.
            *   UC-JS-LOGIN-04: Handle login errors, such as incorrect credentials, locked account, etc., with appropriate error messages.
            *   UC-JS-LOGIN-05: Implement "Remember Me" functionality for subsequent logins.
    *   **FR-JS-ACC-003:** Password Reset
        *   **Description:** Users should be able to reset their passwords if they forget them.
        *   **Use Cases:**
            *   UC-JS-PWD-01: Request a password reset via email. The system should send a password reset link to the registered email address.
            *   UC-JS-PWD-02: Request a password reset via mobile number. The system should send an OTP (One-Time Password) to the registered mobile number.
            *   UC-JS-PWD-03: Enter a new password that meets the defined password complexity requirements.
    *   **FR-JS-ACC-004:** Profile Update
        *   **Description:** Users can update their profile information.
        *   **Use Cases:**
            *   UC-JS-PROF-01: Edit personal details (name, date of birth, nationality).
            *   UC-JS-PROF-02: Update contact information (email, phone number, address).
            *   UC-JS-PROF-03: Add/edit skills, education, and work experience.
            *   UC-JS-PROF-04: Specify desired job type, industry, location preferences, and salary expectations.
            *   UC-JS-PROF-05: Upload a profile picture.
            *   UC-JS-PROF-06: Manage notification preferences.
    *   **FR-JS-ACC-005:** Profile Visibility
        *   **Description:** Users can control the visibility of their profile.
        *   **Use Cases:**
            *   UC-JS-VIS-01: Set profile visibility to "Public" (visible to all registered employers).
            *   UC-JS-VIS-02: Set profile visibility to "Private" (only visible to the user and when applying for jobs).
            *   UC-JS-VIS-03: Set profile visibility to "Partially Visible" (select which sections of the profile are public/private).

*   **3.1.2 Resume Management (Expanded)**
    *   **FR-JS-RES-001:** Resume Upload
        *   **Description:** Users should be able to upload multiple resumes.
        *   **Use Cases:**
            *   UC-JS-RES-01: Upload a resume in PDF format.
            *   UC-JS-RES-02: Upload a resume in DOC or DOCX format.
            *   UC-JS-RES-03: Handle errors related to file size, format, or corrupted files.
            *   UC-JS-RES-04: View a preview of the uploaded resume.
    *   **FR-JS-RES-002:** Resume Builder
        *   **Description:** Provide a built-in tool to create resumes.
        *   **Use Cases:**
            *   UC-JS-RESB-01: Enter personal details, contact information, education, and work experience in structured fields.
            *   UC-JS-RESB-02: Choose from pre-defined resume templates.
            *   UC-JS-RESB-03: Customize the resume layout, fonts, and colors.
            *   UC-JS-RESB-04: Download the created resume in PDF format.

*   **3.1.3 Job Search and Filtering (Expanded)**
    *   **FR-JS-SCH-001:** Job Search
        *   **Description:** Users can perform keyword-based searches for jobs.
        *   **Use Cases:**
            *   UC-JS-SCH-01: Search by job title, keywords, skills, or company name.
            *   UC-JS-SCH-02: Search with multiple keywords using boolean operators (AND, OR, NOT).
            *   UC-JS-SCH-03: Search for jobs within a specific location (city, area, or radius).
            *   UC-JS-SCH-04: Handle empty search queries and provide appropriate feedback.
            *   UC-JS-SCH-05: Implement auto-suggestions for keywords and locations.
    *   **FR-JS-SCH-002:** Search Filters
        *   **Description:** Provide various filters to refine search results.
        *   **Use Cases:**
            *   UC-JS-FLT-01: Filter by job category/industry (e.g., IT, Engineering, Healthcare, Finance).
            *   UC-JS-FLT-02: Filter by location using a map or list of cities/areas.
            *   UC-JS-FLT-03: Filter by salary range (minimum and maximum).
            *   UC-JS-FLT-04: Filter by experience level (Entry-level, Mid-level, Senior-level).
            *   UC-JS-FLT-05: Filter by date posted (last 24 hours, last 7 days, last 30 days).
            *   UC-JS-FLT-06: Filter by job type (Full-time, Part-time, Contract, Internship, Freelance).
            *   UC-JS-FLT-07: Filter by company type (Government, Private, Multinational).

*   **3.1.4 Job Application (Expanded)**
    *   **FR-JS-APP-001:** Job Application Process
        *   **Description:** Users can apply for jobs directly through the portal.
        *   **Use Cases:**
            *   UC-JS-APP-01: Click on an "Apply" button for a specific job posting.
            *   UC-JS-APP-02: Select a resume to attach from uploaded resumes or upload a new one.
            *   UC-JS-APP-03: Write or upload a cover letter (optional).
            *   UC-JS-APP-04: Answer pre-screening questions set by the employer (if any).
            *   UC-JS-APP-05: Submit the application and receive a confirmation message.
            *   UC-JS-APP-06: Handle errors during application submission (e.g., network issues, invalid data).

*   **3.1.6 Job Alerts (Expanded)**
    *   **FR-JS-ALT-001:** Create Job Alert
        *   **Description:** Users can create job alerts based on their search criteria.
        *   **Use Cases:**
            *   UC-JS-ALT-01: Save a current search query as a job alert.
            *   UC-JS-ALT-02: Manually define the criteria for a new job alert (keywords, location, salary, etc.).
            *   UC-JS-ALT-03: Set the frequency of alerts (daily, weekly).
            *   UC-JS-ALT-04: Name the job alert for easy identification.

## 3.2 Employer Functionality (Expanded)

*   **3.2.1 Company Profile Management**
    *   **FR-ER-PRO-001:** Company Registration & Profile Creation
        *   **Description:** Employers can register and create detailed company profiles.
        *   **Use Cases:**
            *   UC-ER-REG-01: Register as an employer, providing company details including:
                * Company name and legal information
                * Industry sector and company size
                * Contact details and physical address
                * Company description and culture
                * Social media links and website
            *   UC-ER-REG-02: Upload company logo and banner images
            *   UC-ER-REG-03: Add company locations (multiple office locations if applicable)
            *   UC-ER-REG-04: Verify company email domain and business registration
    *   **FR-ER-PRO-002:** Profile Management
        *   **Description:** Employers can manage their company profile and team members.
        *   **Use Cases:**
            *   UC-ER-PROF-01: Update company information and media
            *   UC-ER-PROF-02: Add and manage team members with different roles
            *   UC-ER-PROF-03: Set up approval workflows for job postings
            *   UC-ER-PROF-04: Configure company-wide preferences and settings

*   **3.2.2 Job Posting Management**
    *   **FR-ER-POST-001:** Create Job Posting
        *   **Description:** Employers can create detailed job postings.
        *   **Use Cases:**
            *   UC-ER-POST-01: Create a new job posting with:
                * Job title and department
                * Job description and requirements
                * Required skills and qualifications
                * Salary range and benefits
                * Location and work type
            *   UC-ER-POST-02: Add screening questions
            *   UC-ER-POST-03: Set application deadline
            *   UC-ER-POST-04: Preview posting before publishing
    *   **FR-ER-POST-002:** Manage Job Postings
        *   **Description:** Employers can manage their active job postings.
        *   **Use Cases:**
            *   UC-ER-MPOST-01: Edit existing job postings
            *   UC-ER-MPOST-02: Pause or close job postings
            *   UC-ER-MPOST-03: Duplicate existing postings
            *   UC-ER-MPOST-04: View posting analytics and performance

*   **3.2.3 Candidate Management**
    *   **FR-ER-CAN-001:** Application Review
        *   **Description:** Employers can review and manage job applications.
        *   **Use Cases:**
            *   UC-ER-APP-01: View all applications for a job posting
            *   UC-ER-APP-02: Filter and sort applications
            *   UC-ER-APP-03: Rate and comment on applications
            *   UC-ER-APP-04: Move candidates through hiring stages
    *   **FR-ER-CAN-002:** Candidate Communication
        *   **Description:** Employers can communicate with candidates.
        *   **Use Cases:**
            *   UC-ER-COM-01: Send messages to candidates
            *   UC-ER-COM-02: Schedule interviews
            *   UC-ER-COM-03: Send status updates
            *   UC-ER-COM-04: Track communication history

## 3.3 Administrator Functionality (Expanded)

*   **3.3.1 User Management**
    *   **FR-AD-USR-001:** User Administration
        *   **Description:** Administrators can manage all user accounts.
        *   **Use Cases:**
            *   UC-AD-USR-01: View and manage user accounts
            *   UC-AD-USR-02: Suspend or delete user accounts
            *   UC-AD-USR-03: Reset user passwords
            *   UC-AD-USR-04: Manage user roles and permissions

*   **3.3.2 Content Management**
    *   **FR-AD-CON-001:** Content Moderation
        *   **Description:** Administrators can moderate user-generated content.
        *   **Use Cases:**
            *   UC-AD-CON-01: Review job postings for compliance
            *   UC-AD-CON-02: Review resumes for completeness
            *   UC-AD-CON-03: Approve or reject user-generated content

*   **3.3.3 System Configuration**
    *   **FR-AD-SYS-001:** System Settings
        *   **Description:** Administrators can manage system settings.
        *   **Use Cases:**
            *   UC-AD-SYS-01: Configure email settings
            *   UC-AD-SYS-02: Configure payment gateways
            *   UC-AD-SYS-03: Configure security settings

*   **3.3.4 Reporting and Analytics**
    *   **FR-AD-RPT-001:** System Analytics
        *   **Description:** Administrators can access system reports and analytics.
        *   **Use Cases:**
            *   UC-AD-ANA-01: View system usage metrics
            *   UC-AD-ANA-02: View user activity metrics
            *   UC-AD-ANA-03: View job posting statistics

*   **3.3.5 Customer Support Management**
    *   **FR-AD-SUP-001:** Support Ticket Management
        *   **Description:** Administrators can manage support tickets.
        *   **Use Cases:**
            *   UC-AD-SUP-01: View and manage support tickets
            *   UC-AD-SUP-02: Assign tickets to support agents
            *   UC-AD-SUP-03: Track ticket status and resolution

*   **3.3.6 Payment Management**
    *   **FR-AD-PAY-001:** Payment Processing
        *   **Description:** Administrators can manage payment transactions.
        *   **Use Cases:**
            *   UC-AD-PAY-01: View and manage transactions
            *   UC-AD-PAY-02: Process refunds and cancellations
            *   UC-AD-PAY-03: Generate invoices and receipts
