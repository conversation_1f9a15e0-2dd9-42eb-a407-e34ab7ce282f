# 8. Task ID Naming Conventions

## 8.1 Overview

This document outlines the task identification (Task ID) naming strategy implemented across the Qatar Jobs Portal project documentation. The naming convention is designed to provide a consistent, scalable, and intuitive way to reference requirements, features, and tasks throughout the project lifecycle.

## 8.2 Task ID Structure

### 8.2.1 Basic Format
The Task ID follows this general pattern:
```
[TYPE]-[CATEGORY]-[SUBCATEGORY]-[NUMBER]
```

Example: `FR-JS-ACC-001` (Functional Requirement - Job Seeker - Account - Number 001)

### 8.2.2 Components Breakdown

1. **Type Prefix**
   - FR: Functional Requirements
   - NFR: Non-Functional Requirements
   - FE: Future Enhancements
   - OI: Open Issues

2. **Category**
   - Represents the main functional area or domain
   - 2-4 characters in length
   - Always uppercase

3. **Subcategory**
   - Further classifies the requirement within its category
   - 2-4 characters in length
   - Always uppercase

4. **Number**
   - Three-digit sequential number (001-999)
   - Allows for future insertions without disrupting sequence

## 8.3 Category Codes

### 8.3.1 Functional Requirements (FR)
- JS: Job Seeker
- ER: Employer
- AD: Administrator
- SA: Super Administrator
- SCH: Search
- NOT: Notifications
- REP: Reporting

### 8.3.2 Non-Functional Requirements (NFR)
- PERF: Performance
- SEC: Security
- USA: Usability
- REL: Reliability
- SCA: Scalability
- MAIN: Maintainability
- INT: Internationalization
- LEG: Legal
- ENV: Environmental
- DOC: Documentation

### 8.3.3 Subcategory Examples
- RT: Response Time
- CAP: Capacity
- AUTH: Authentication
- UI: User Interface
- SYS: System
- API: API Interface
- LOG: Logging
- MON: Monitoring

## 8.4 Benefits and Rationale

### 8.4.1 Clarity and Organization
1. **Hierarchical Structure**
   - Clear indication of requirement type
   - Logical grouping by functional area
   - Easy to understand relationships between requirements

2. **Searchability**
   - Quick filtering by type, category, or subcategory
   - Easy to locate related requirements
   - Efficient documentation navigation

### 8.4.2 Scalability
1. **Extensible Design**
   - New categories can be added without disrupting existing IDs
   - Subcategories can be expanded as needed
   - Number sequence allows for insertion of new requirements

2. **Version Control**
   - Easy to track requirement changes
   - Simple to reference in commit messages
   - Clear audit trail for requirement evolution

### 8.4.3 Project Management
1. **Traceability**
   - Links between requirements and implementation
   - Clear mapping to test cases
   - Easy reference in technical discussions

2. **Documentation**
   - Consistent reference format across all documents
   - Simple to cross-reference requirements
   - Clear communication among team members

## 8.5 Usage Guidelines

### 8.5.1 Creating New Task IDs
1. **Sequence Numbers**
   - Start with 001 for each category/subcategory combination
   - Increment sequentially
   - Leave gaps (e.g., increment by 5) for future insertions

2. **Category Selection**
   - Choose the most specific applicable category
   - Create new categories only when necessary
   - Maintain consistency with existing categories

### 8.5.2 Best Practices
1. **Consistency**
   - Always use uppercase for category and subcategory codes
   - Maintain three digits in sequence numbers
   - Follow established patterns for new IDs

2. **Documentation**
   - Include Task ID in all related documentation
   - Reference Task IDs in technical discussions
   - Use Task IDs in commit messages and pull requests

3. **Maintenance**
   - Regularly review and update category lists
   - Document new categories and subcategories
   - Maintain backwards compatibility

## 8.6 Examples

### 8.6.1 Functional Requirements
```
FR-JS-ACC-001: Job seeker registration
FR-ER-POST-005: Job posting creation
FR-AD-MOD-003: Content moderation
```

### 8.6.2 Non-Functional Requirements
```
NFR-PERF-RT-001: Page load performance
NFR-SEC-AUTH-002: Data protection
NFR-USA-UI-001: Accessibility standards
```

### 8.6.3 Future Enhancements
```
FE-AI-MATCH-001: AI-powered job matching
FE-CHAT-BOT-002: Automated support chatbot
FE-MOB-APP-001: Mobile application development
```

## 8.7 Version History

| Version | Date | Description |
|---------|------|-------------|
| 1.0 | 2024-01-25 | Initial version of Task ID naming convention document |

## 8.8 Related Documents
- 3-functional-requirements.md
- 4-non-functional-requirements.md
- 5-open-issues.md
- 6-future-enhancements.md
