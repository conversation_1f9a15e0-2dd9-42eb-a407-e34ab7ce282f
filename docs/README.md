# Qatar Jobs Portal - Software Requirements Specification

This directory contains the Software Requirements Specification (SRS) documentation for the Qatar Jobs Portal project. The documentation has been split into separate files for better organization and easier navigation.

## Document Structure

1. [Introduction](1-introduction.md)
2. [System Description](2-system-description.md)
3. [Functional Requirements](3-functional-requirements.md)
4. [Non-Functional Requirements](4-non-functional-requirements.md)
5. [Open Issues](5-open-issues.md)
6. [Future Enhancements](6-future-enhancements.md)
7. [Glossary](7-glossary.md)

## How to Use This Documentation

Each section is contained in its own markdown file for easier maintenance and version control. To get a complete understanding of the system requirements:

1. Start with the Introduction to understand the purpose and scope
2. Review the System Description for a high-level overview
3. Dive into the Functional Requirements for detailed feature specifications
4. Check the Non-Functional Requirements for system qualities and constraints
5. Review Open Issues and Future Enhancements for ongoing considerations
6. Reference the Glossary for terminology definitions

## Document Maintenance

This documentation will be regularly reviewed and updated as the project evolves. All stakeholders should refer to these documents for the latest requirements and specifications.
